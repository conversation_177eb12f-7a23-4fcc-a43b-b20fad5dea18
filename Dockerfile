# Use OpenJDK 21 slim as base image
FROM openjdk:21-slim

# Install Git (required by the server application)
RUN apt-get update && apt-get install -y git && rm -rf /var/lib/apt/lists/*

# Configure Git user (required by flow tracking) - use same config as CI
RUN git config --global user.name "CI Bot" && \
    git config --global user.email "<EMAIL>"


# Set the working directory to match the expected project path
WORKDIR /app

# Copy the entire project structure including Git for flow tracking
COPY . /app/
COPY target/robot-world-*-jar-with-dependencies.jar /app/robot-worlds-server.jar

# Expose port 5050
EXPOSE 5050

# Set the entry point to run the JAR file
ENTRYPOINT ["java", "-jar", "/app/robot-worlds-server.jar"]

# Default command arguments - run on port 5050 for Docker (can be overridden)
CMD ["-p", "5050"]
