.PHONY: clean build test test-all-except-acceptance release tag all bump-patch bump-minor bump-major commit-and-push

# File paths
MAKEFILE_DIR := $(shell dirname $(realpath $(lastword $(MAKEFILE_LIST))))
version_file = $(MAKEFILE_DIR)/VERSION
current_version = $(shell cat $(version_file))
dev_ver = $(current_version)-SNAPSHOT

define bump_version
$(eval MAJOR=$(shell echo $(current_version) | cut -d. -f1))
$(eval MINOR=$(shell echo $(current_version) | cut -d. -f2))
$(eval PATCH=$(shell echo $(current_version) | cut -d. -f3))
endef

# Clean target
clean:
	mvn -f ../pom.xml clean

# Build target (skip tests)
build:
	mvn  -f ../pom.xml clean install -DskipTests

# Test target
test-ref-server:
	@echo "Testing with reference server..."
	@sleep 5
	@which lsof
	@lsof -ti:5000 || true
	SERVER_JAR_PATH=./libs/reference-server-0.2.3.jar \
	DEBUG=false \
	mvn -f ../pom.xml test \
	     -Dtest="LaunchRobotTests,RobotLookTests,RobotStateTests,RobotMovementTests"
	@$(MAKE) kill-port-5000


unit-test:
	@echo "Running all tests except acceptance tests..."
	mvn -f ../pom.xml test -Dtest="!**/acceptanceTest/**"

test-our-server:
	@echo "Testing with our server..."
	@sleep 5
	@which lsof
	@lsof -ti:5000 || true
	SERVER_JAR_PATH=./libs/robot-worlds-server-$(current_version).jar \
	DEBUG=false \
	mvn -f ../pom.xml test \
	 -Dtest="LaunchRobotTests,RobotLookTests,RobotStateTests,RobotMovementTests"
	@$(MAKE) kill-port-5000

kill-port-5000:
	@echo "Killing anything on port 5000"
    	-@PIDS=$$(lsof -ti:5000) && \
    	if [ -n "$$PIDS" ]; then \
    		echo "Killing PIDs: $$PIDS"; \
    		kill -9 $$PIDS; \
    	fi || true

kill-port-5050:
	@echo "Killing anything on port 5050"
    	-@PIDS=$$(lsof -ti:5050) && \
    	if [ -n "$$PIDS" ]; then \
    		echo "Killing PIDs: $$PIDS"; \
    		kill -9 $$PIDS; \
    	fi || true

acceptance-test: clean kill-port-5000 test-our-server test-ref-server

package: release

# Release target
release:
	@echo "Switching to release version..."
	sed -i "s/<version>$(dev_ver)<\/version>/<version>$(current_version)<\/version>/" ../pom.xml
	mvn -f ../pom.xml clean package -DskipTests
	@echo "Reverting to snapshot version..."
	sed -i "s/<version>$(current_version)<\/version>/<version>$(dev_ver)<\/version>/" ../pom.xml
	@cp ../target/robot-world-$(current_version)-jar-with-dependencies.jar ../libs/robot-worlds-server-$(current_version).jar
	git -C .. add libs/robot-worlds-server-$(current_version).jar

# Git tagging
tag:
	git -C .. tag -a v$(current_version) -m "Release version $(current_version)"
	git -C .. push origin v$(current_version)

# All target
bump-patch:
	@$(call bump_version)
	@bash -c '\
		MAJOR=$(MAJOR); \
		MINOR=$(MINOR); \
		PATCH=$(PATCH); \
		NEW_VERSION=$$MAJOR.$$MINOR.$$((PATCH + 1)); \
		echo "Bumping patch: $(current_version) → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $(version_file); \
		sed -i "s/<version>$(current_version)-SNAPSHOT<\/version>/<version>$$NEW_VERSION-SNAPSHOT<\/version>/" ../pom.xml \
	'

bump-minor:
	@$(call bump_version)
	@bash -c '\
		MAJOR=$(MAJOR); \
		MINOR=$(MINOR); \
		NEW_VERSION=$$MAJOR.$$((MINOR + 1)).0; \
		echo "Bumping minor: $(current_version) → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $(version_file); \
		sed -i "s/<version>$(current_version)-SNAPSHOT<\/version>/<version>$$NEW_VERSION-SNAPSHOT<\/version>/" ../pom.xml \
	'

bump-major:
	@$(call bump_version)
	@bash -c '\
		MAJOR=$(MAJOR); \
		NEW_VERSION=$$((MAJOR + 1)).0.0; \
		echo "Bumping major: $(current_version) → $$NEW_VERSION"; \
		echo $$NEW_VERSION > $(version_file); \
		sed -i "s/<version>$(current_version)-SNAPSHOT<\/version>/<version>$$NEW_VERSION-SNAPSHOT<\/version>/" ../pom.xml \
	'

commit-and-push:
	@git -C .. add .
	@git -C .. commit -m "Bump version to $(current_version)"
	@git -C .. push origin main


all: clean unit-test acceptance-test build release

publish: release commit-and-push tag

publish-patch: bump-patch release commit-and-push tag

publish-minor: bump-minor release commit-and-push tag

publish-major: bump-major release commit-and-push tag
# Docker targets
docker-build:
	@echo "Building Docker image..."
	@cd .. && docker build -t robot-worlds-server:$(current_version) .
	@cd .. && docker tag robot-worlds-server:$(current_version) robot-worlds-server:$(current_version)
	@echo "Docker image built: robot-worlds-server:$(current_version)"

docker-run:
	@echo "Running Docker container on port 5050..."
	@$(MAKE) kill-port-5050
	@cd .. && docker run -d --rm -p 5050:5050 --name robot-server-$(current_version) robot-worlds-server:$(current_version)
	@echo "Container started. Access server at http://localhost:5050"
	@echo "To stop: make docker-stop (container will be auto-removed)"

docker-run-interactive:
	@echo "Running Docker container interactively..."
	@$(MAKE) kill-port-5050
	@cd .. && docker run -it --rm -p 5050:5050 --name robot-server-interactive robot-worlds-server:$(current_version)

docker-stop:
	@echo "Stopping Docker containers (auto-removal enabled)..."
	-@docker stop robot-server-$(current_version) 2>/dev/null || true
	-@docker stop robot-server-interactive 2>/dev/null || true
	-@docker stop robot-test-server 2>/dev/null || true
	@echo "Containers stopped and auto-removed."

docker-clean:
	@echo "Cleaning up Docker containers..."
	-@docker stop robot-server-$(current_version) 2>/dev/null || true
	-@docker stop robot-server-interactive 2>/dev/null || true
	-@docker stop robot-test-server 2>/dev/null || true
	@echo "Containers stopped (auto-removed with --rm flag)."

docker-clean-images:
	@echo "Cleaning up project Docker images..."
	-@docker rmi -f robot-worlds-server:$(current_version) 2>/dev/null || true
	-@docker rmi -f robot-worlds-server:latest 2>/dev/null || true
	@echo "Project images cleaned up (tagged base images preserved)."

docker-clean-all:
	@echo "Cleaning up ALL Docker containers and untagged images..."
	-@docker stop $$(docker ps -aq) 2>/dev/null || true
	-@docker rm -f $$(docker ps -aq) 2>/dev/null || true
	-@docker image prune -f 2>/dev/null || true
	@echo "All containers and untagged images cleaned up (tagged images preserved)."

docker-test:
	@echo "Testing Docker container..."
	@$(MAKE) kill-port-5050
	@echo "Starting Docker container for testing..."
	@cd .. && docker run -d --rm -p 5050:5050 --name robot-test-server robot-worlds-server:$(current_version) -p 5050 -s 2 -o 0,1
	@echo "Waiting for server to start..."
	@sleep 10
	@echo "Running acceptance tests against Docker container..."
	@USE_OWN_SERVER=true SERVER_PORT=5050 mvn -f ../pom.xml test -Dtest="LaunchRobotTests,RobotLookTests,RobotStateTests,RobotMovementTests" || (docker stop robot-test-server; exit 1)
	@echo "Cleaning up test container..."
	@docker stop robot-test-server
	@echo "Docker tests completed successfully! (container auto-removed)"

docker-login:
	@echo "Logging into GitLab Container Registry..."
	@echo "Please enter your GitLab credentials when prompted."
	@docker login gitlab.wethinkco.de:5050

docker-push:
	@echo "Pushing Docker image to GitLab Container Registry..."
	@echo "Make sure you're logged in: make docker-login"
	@GITLAB_PROJECT=nhmasiljhb024/brownfields-robot-worlds-jhb-01 && \
	cd .. && \
	docker tag robot-worlds-server:$(current_version) gitlab.wethinkco.de:5050/$$GITLAB_PROJECT/robot-worlds-server:$(current_version) && \
	docker tag robot-worlds-server:$(current_version) gitlab.wethinkco.de:5050/$$GITLAB_PROJECT/robot-worlds-server:latest && \
	docker push gitlab.wethinkco.de:5050/$$GITLAB_PROJECT/robot-worlds-server:$(current_version) && \
	docker push gitlab.wethinkco.de:5050/$$GITLAB_PROJECT/robot-worlds-server:latest
	@echo "Docker image pushed successfully!"

# Combined targets
docker-all: docker-build docker-test
docker-release: docker-build docker-test docker-login docker-push
