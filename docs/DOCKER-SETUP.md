# Docker Deployment Steps - Robot Worlds Server

## Goal: Containerize and Deploy Robot Worlds Server

### Step 1: Create Dockerfile

Create a `Dockerfile` in your project root using multi-stage build:

```dockerfile

FROM eclipse-temurin:21-jre-alpine

# Install curl for health checks

RUN apk add --no-cache curl

# Create non-root user

RUN addgroup -g 1001 appuser && \
\
adduser -D -u 1001 -G appuser appuser

# Copy JAR and configuration files

COPY target/robot-worlds-server-*-jar-with-dependencies.jar
/app/robot-worlds-server.jar

COPY src/main/resources/ /app/

# Set working directory and permissions

WORKDIR /app

RUN chown -R appuser:appuser /app

# Switch to non-root user

USER appuser

# Expose port 5050 (as specified in requirements)

EXPOSE 5050

# Health check

HEALTHCHECK --interval=30s --timeout=10s --start-period=5s
--retries=3 \

CMD curl -f http://localhost:5050/health || exit 1

# Define startup command

CMD ["java", "-jar", "robot-worlds-server.jar", "-p",
"5050"]

```

### Step 2: Build Your Application

```bash

# Clean and build the JAR

mvn clean package -DskipTests

# Or use the Makefile (if available)

cd scripts

make build

```

### Step 3: Build Docker Image

```bash

# Build Docker image with version tag

docker build -t robot-worlds-server:0.1.4 .

# Or use the Makefile

make docker-build

```

### Step 4: Test Locally

```bash

# Run container locally (map port 5050 to host port 5050)

docker run -p 5050:5050 robot-worlds-server:0.1.4

# Run with custom arguments

docker run -p 5050:5050 robot-worlds-server:0.1.4 -p 5050 -s 2 -o
mountain

# Test in another terminal

curl http://localhost:5050

```

### Step 5: Set Up GitLab Container Registry

#### 5.1 Use Existing GitLab Project

- Navigate to your existing GitLab project at
(https://gitlab.wethinkco.de)

- Ensure you have maintainer/owner permissions for container registry
access

#### 5.2 Create Personal Access Token

- Go to GitLab Settings → Access Tokens

- Create token with permissions: `read_registry` and
`write_registry`

- Save the token securely

#### 5.3 Authenticate Docker with GitLab Registry

```bash

# Login to GitLab container registry

docker login -u <your-username> -p <personal-access-token>
gitlab.wethinkco.de:5050

```

### Step 6: Publish Image to Registry

```bash

# Tag image for GitLab registry with version and latest

docker tag robot-worlds-server:0.1.4
gitlab.wethinkco.de:5050/<namespace>/<project>/robot-worlds-server:0.1.4

docker tag robot-worlds-server:0.1.4
gitlab.wethinkco.de:5050/<namespace>/<project>/robot-worlds-server:latest

# Push to registry

docker push
gitlab.wethinkco.de:5050/<namespace>/<project>/robot-worlds-server:0.1.4

docker push
gitlab.wethinkco.de:5050/<namespace>/<project>/robot-worlds-server:latest

```

### Step 7: Test Deployment

```bash

# Pull and run published image

docker pull
gitlab.wethinkco.de:5050/<namespace>/<project>/robot-worlds-server:latest

docker run -p 5050:5050
gitlab.wethinkco.de:5050/<namespace>/<project>/robot-worlds-server:latest

```

### Step 8: Run Acceptance Tests

```bash

# Start Docker container in background

docker run -d -p 5050:5050 --name test-server robot-worlds-server:0.1.4

# Wait for server to be ready

sleep 5

# Run acceptance tests

mvn test -Dtest="za.co.wethinkcode.robots.acceptanceTest.*"

# Clean up

docker stop test-server

docker rm test-server

```

### Step 9: Update Makefile

Add these targets to your `Makefile`:

```makefile

# Build Docker image

docker-build:
```
mvn clean package -DskipTests
```

docker build -t robot-worlds-server:0.1.4 .

0.1.4:

# Run Docker container locally

docker-run:

docker run -p 5050:5050 robot-worlds-server:0.1.4

# Run Docker container with custom arguments

docker-run-custom:

docker run -p 5050:5050 robot-worlds-server:0.1.4 -p 5050 -s 2 -o
mountain

# Publish to GitLab registry

docker-publish:

docker tag robot-worlds-server:0.1.4
gitlab.wethinkco.de:5050/<namespace>/<project>/robot-worlds-server:0.1.4

docker tag robot-worlds-server:0.1.4
gitlab.wethinkco.de:5050/<namespace>/<project>/robot-worlds-server:latest

docker push
gitlab.wethinkco.de:5050/<namespace>/<project>/robot-worlds-server:0.1.4

docker push gitlab.wethinkco.de:5050/

<namespace>/<project>/robot-worlds-server:latest

# Run acceptance tests against Docker container

docker-test:

docker run -d -p 5050:5050 --name test-server robot-worlds-server:0.1.4

sleep 5

mvn test -Dtest="za.co.wethinkcode.robots.acceptanceTest.*"

docker stop test-server

docker rm test-server

# Full Docker workflow

docker-test-full:

make docker-build

make docker-test

# Clean up Docker resources

docker-clean:

docker stop $$(docker ps -q --filter
ancestor=robot-worlds-server:0.1.4) 2>/dev/null || true

docker rm $$(docker ps -aq --filter
ancestor=robot-worlds-server:0.1.4) 2>/dev/null || true

```

### Step 10: Team Verification

Every team member should be able to:

1. Pull the published image: `docker pull
gitlab.wethinkco.de:5050/<namespace>/<project>/robot-worlds-server:latest`

2. Run it locally: `docker run -p 5050:5050
gitlab.wethinkco.de:5050/<namespace>/<project>/robot-worlds-server:latest`

3. Connect client to `localhost:5050`

4. Verify admin console works

5. Run acceptance tests against the container

## Tasks Checklist

### Prerequisites

- [ ] Docker installed on your system

- [ ] GitLab account with access to WTC GitLab Container Registry

- [ ] Personal Access Token with `read_registry` and
`write_registry` permissions

- [ ] Maven 3.9.6+ and Java 21

### Development Tasks

- [ ] Create Dockerfile in project root

- [ ] Configure server to run on port 5050 by default

- [ ] Build application JAR with Maven

- [ ] Build Docker image locally

- [ ] Test Docker container locally

- [ ] Verify container accepts command-line arguments

### Registry Setup

- [ ] Use existing GitLab project for container registry

- [ ] Verify you have maintainer/owner permissions

- [ ] Create Personal Access Token with correct permissions

- [ ] Authenticate Docker with GitLab registry

- [ ] Tag image with proper registry URL

- [ ] Push image to GitLab Container Registry

### Testing

- [ ] Pull published image and run locally

- [ ] Run acceptance tests against Docker container

- [ ] Verify health check endpoint works

- [ ] Test container with different configurations

### Team Collaboration

- [ ] Each team member can pull the image

- [ ] Each team member can run the container locally

- [ ] Each team member can connect client to containerized server

- [ ] Each team member can run acceptance tests against container

### Makefile Integration

- [ ] Add docker-build target

- [ ] Add docker-run target

- [ ] Add docker-publish target

- [ ] Add docker-test target

- [ ] Add docker-clean target

- [ ] Test all Makefile targets

### Optional CI/CD Integration

- [ ] Integrate Docker build into GitLab CI pipeline

- [ ] Automatically tag images with commit

SHA

- [ ] Automatically push images on successful tests

- [ ] Set up automated testing against Docker images

## Container Management Commands

### Development

```bash

# Build and test locally

make docker-build

make docker-test

# Run with custom settings

docker run -p 5050:5050 robot-worlds-server:0.1.4 -p 5050 -s 3 -o pit

# Debug container

docker exec -it <container-name> /bin/sh

```

### Production

```bash

# Run in background

docker run -d -p 5050:5050 --name robot-server
robot-worlds-server:0.1.4

# View logs

docker logs robot-server

# Stop and remove

docker stop robot-server && docker rm robot-server

```

## Key Points to Remember

- Server must run on port 5050 inside container

- Use `eclipse-temurin:21-jre-alpine` base image for Java 21

- Implement health checks for container

monitoring

- Use non-root user for security

- Support command-line arguments for configuration

- All team members must demonstrate they can run the image

- Add Docker commands to Makefile for easy execution

- Use version tags (0.1.4) and latest tag for images
