# Contributing to Robot World

Thank you for your interest in contributing to Robot World! This document provides comprehensive guidelines and information for contributors working on this game-like simulation project.

## 🚀 Getting Started

### Prerequisites
- **Java 21** (Amazon Corretto recommended)
- **Maven 3.9.6+** (included in project at `./apache-maven-3.9.6/`)
- **Git** for version control
- **GitLab account** for issue tracking and merge requests
- **CodeScene access** for code quality analysis

### Setting Up Development Environment
1. **Fork the repository** on GitLab
2. **Clone your fork**:
   ```bash
   <NAME_EMAIL>:your-username/brownfields-robot-worlds-jhb-01.git
   cd brownfields-robot-worlds-jhb-01
   ```
3. **Set up Maven** (if not using included version):
   ```bash
   # Use included Maven
   chmod +x mvn.sh
   ./mvn.sh --version

   # Or install system-wide Maven 3.9.6+
   ```
4. **Build the project**:
   ```bash
   mvn clean compile
   ```
5. **Run all tests**:
   ```bash
   mvn test
   ```
6. **Run acceptance tests**:
   ```bash
   java -jar ./.lib/reference-server-0.1.0.jar
   ```

### 🎯 Current Iteration Goals
Before contributing, please review our [Iteration Goals](ITERATION_GOALS.md) which outline:
- GitLab taskboard setup and backlog management
- Acceptance test implementation requirements
- Build script automation standards
- Code quality analysis with CodeScene

## 📋 Issue Management & GitLab Taskboard

### GitLab Taskboard Workflow
We use GitLab's issue board to manage our Robot World backlog and track progress:

#### Board Columns
- **Backlog** - All identified features and requirements
- **Ready for Dev** - Prioritized items ready for current iteration
- **In Dev** - Currently being worked on (WIP limit: 3 per person)
- **In Testing** - Under code review and testing (WIP limit: 5 total)
- **Ready for Showcase** - Completed and ready for demonstration
- **Done** - Completed, showcased, and merged

### Creating Issues
We use structured issue templates to maintain consistency. When creating an issue, please:

1. **Choose the appropriate template**:
   - 🐛 **Bug Report** - For reporting bugs and defects
   - ✨ **Feature Request** - For new Robot World features
   - 🚀 **Enhancement** - For improvements to existing features
   - 📚 **Documentation** - For documentation issues
   - ❓ **Question** - For support questions
   - 🧪 **Acceptance Test** - For test implementation tasks
   - 🏗️ **Build/DevOps** - For build script and automation tasks

2. **Fill out all relevant sections** in the template
3. **Use appropriate labels** (they'll be auto-applied from templates)
4. **Link related issues** when applicable
5. **Add to appropriate milestone** for iteration tracking

### Issue Labels

#### Type Labels
- `type::bug` 🔴 - Critical bugs requiring immediate attention
- `type::feature` 🔵 - New functionality requests
- `type::enhancement` 🟢 - Improvements to existing features
- `type::documentation` 🟡 - Documentation related issues
- `type::question` 🟣 - Support questions
- `type::acceptance-test` 🧪 - Acceptance test implementation
- `type::build` 🏗️ - Build scripts and automation

#### Priority Labels
- `priority::critical` 🔴 - Must be fixed immediately
- `priority::high` 🟠 - Should be fixed soon
- `priority::medium` 🟡 - Normal priority
- `priority::low` 🟢 - Can be addressed later

#### Status Labels (Scoped)
- `status::ready-for-dev` - Ready to be worked on
- `status::in-dev` - Currently being worked on
- `status::in-testing` - Under code review and testing
- `status::ready-for-showcase` - Ready for demonstration
- `status::blocked` - Blocked by dependencies

#### Component Labels
- `component::server` - Server-side code
- `component::client` - Client-side code
- `component::world` - World simulation logic
- `component::robot` - Robot behavior and logic
- `component::tests` - Testing related
- `component::build` - Build scripts and CI/CD
- `component::docs` - Documentation

## 🔄 Development Workflow

### Branching Strategy
- `main` - Production-ready code
- `develop` - Integration branch for features
- `feature/issue-number-description` - Feature branches
- `bugfix/issue-number-description` - Bug fix branches
- `hotfix/issue-number-description` - Critical fixes

### Making Changes
1. **Create a branch** from `develop`:
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b feature/123-add-robot-command
   ```

2. **Make your changes** following our coding standards
3. **Write/update tests** for your changes
4. **Update documentation** if needed
5. **Commit your changes** with clear messages:
   ```bash
   git commit -m "feat: add new robot command for shield management

   - Implement shield repair command
   - Add validation for shield strength
   - Update command handler tests
   
   Closes #123"
   ```

### Commit Message Format
```
type(scope): brief description

Detailed description of changes made.
Include motivation and context.

Closes #issue-number
```

**Types**: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

## 🧪 Testing Framework

### Test Types and Requirements

#### Unit Tests
```bash
# Run all unit tests
mvn test

# Run specific test class
mvn test -Dtest=WorldTest

# Run with coverage report
mvn test jacoco:report
```

#### Acceptance Tests
Acceptance tests validate server compliance with Robot World protocol:
```bash
# Start reference server for comparison
java -jar ./.lib/reference-server-0.1.0.jar

# Run acceptance test suite
mvn test -Dtest=AcceptanceTest*

# Run specific acceptance test
mvn test -Dtest=RobotMovementAcceptanceTest
```

#### Integration Tests
```bash
# Run integration tests
mvn integration-test

# Run all tests including integration
mvn verify
```

### Test Requirements
- **Unit tests** for all new functionality (minimum 80% coverage)
- **Acceptance tests** for Robot World protocol compliance
- **Integration tests** for complex server-client interactions
- **Performance tests** for concurrent robot operations
- **All tests must pass** before submitting MR
- **Test data cleanup** after each test execution

### Test Standards
- Use descriptive test method names: `should_MoveRobot_When_ValidMoveCommand()`
- Follow AAA pattern: Arrange, Act, Assert
- Mock external dependencies appropriately
- Include edge cases and error scenarios
- Document complex test scenarios

## 📝 Code Standards & Quality

### Java Coding Standards
- **Follow Oracle Java conventions** for naming and formatting
- **Use meaningful names**: `robotPosition` not `rp`
- **Add JavaDoc** for all public methods and classes
- **Keep methods focused** and under 20 lines when possible
- **Use appropriate design patterns** (Strategy, Command, Observer)
- **Prefer composition over inheritance**
- **Use immutable objects** where possible

### Code Quality Checks
- **No compiler warnings** allowed
- **No unused imports** or variables
- **Proper exception handling** with meaningful messages
- **Consistent formatting** (use IDE auto-format)
- **No magic numbers** - use named constants
- **Null safety** - validate inputs and use Optional where appropriate

### CodeScene Analysis Integration
We use CodeScene to monitor code quality and identify hotspots:

#### Quality Metrics Monitored
- **Code Complexity** - Keep cyclomatic complexity under 10
- **Code Duplication** - Eliminate duplicate code blocks
- **Technical Debt** - Address identified debt items
- **Test Coverage** - Maintain minimum 80% coverage
- **Hotspots** - Focus on top 3 identified problem areas

#### Before Submitting Code
1. **Run CodeScene analysis** on your changes
2. **Address any new hotspots** introduced
3. **Ensure complexity metrics** remain within acceptable ranges
4. **Document any technical debt** with TODO comments and issues

## 🔍 Code Review Process

### Submitting Merge Requests
1. **Use the MR template** and fill out all sections
2. **Link to related issues**
3. **Ensure all tests pass**
4. **Request review** from team members
5. **Address feedback** promptly

### Review Criteria
- Code quality and standards compliance
- Test coverage and quality
- Documentation updates
- Performance considerations
- Security implications

## 🏷️ Issue Limits and Workflow

### Work in Progress (WIP) Limits
To maintain focus and quality:
- **Maximum 3 issues** in `status::in-dev` per contributor
- **Maximum 5 issues** in `status::in-testing` at any time
- **Maximum 3 issues** in `status::ready-for-showcase` at any time
- **Priority issues** take precedence over lower priority ones

### Issue Lifecycle
1. **Created** → `status::ready-for-dev`
2. **Assigned** → `status::in-dev`
3. **Development Complete** → `status::in-testing`
4. **Testing Complete** → `status::ready-for-showcase`
5. **Showcased & Approved** → **Done** (Closed)

### Showcase Process
Items in `status::ready-for-showcase` require demonstration before completion:

#### Showcase Requirements
- **Working demonstration** of the implemented feature
- **Code walkthrough** highlighting key implementation details
- **Test results** showing all acceptance criteria are met
- **Documentation updates** completed and reviewed
- **Stakeholder approval** from product owner or team lead

#### Showcase Format
- **Demo Environment**: Use staging/demo server setup
- **Duration**: 5-10 minutes per feature
- **Audience**: Team members, stakeholders, product owner
- **Documentation**: Record key decisions and feedback

## 🏗️ Build Scripts & Automation

### Build Script Requirements
All code changes must work with our automated build process:

```bash
# Full build pipeline
./scripts/build.sh

# Individual build steps
mvn clean compile          # Compile source code
mvn test                   # Run unit tests
mvn integration-test       # Run acceptance tests
mvn package               # Create deployable artifacts
mvn verify                # Validate build quality
```

### Build Validation
Before submitting merge requests:
- [ ] **Build script runs successfully**
- [ ] **All tests pass** (unit, integration, acceptance)
- [ ] **Artifacts are generated** correctly
- [ ] **No build warnings** or errors
- [ ] **CodeScene analysis** shows no regressions

### Continuous Integration
- **All commits** trigger automated builds
- **Merge requests** require passing builds
- **Quality gates** must be satisfied
- **Deployment artifacts** are automatically versioned

## 🆘 Getting Help

### Support Channels
- **Questions**: Create an issue with the Question template
- **Discussions**: Use GitLab discussions for broader topics
- **Team Contact**: Tag `@project-maintainer` in issues
- **Code Review**: Request review from `@code-reviewers`

### Resources
- **Project Documentation**: See [README.md](../README.md)
- **Iteration Goals**: See [ITERATION_GOALS.md](ITERATION_GOALS.md)
- **GitLab Board**: [Robot World Taskboard](https://gitlab.wethinkco.de/kumangajhb024/oop-ex-toy-robot-group/-/boards)
- **CodeScene Dashboard**: [Code Quality Analysis](https://codescene.io)

## 📜 License

By contributing, you agree that your contributions will be licensed under the same license as the project.

---

## 🎯 Quick Checklist for Contributors

### Development Phase (`status::in-dev`)
- [ ] **Issue created** and properly labeled
- [ ] **Branch created** from `develop` with proper naming
- [ ] **Code follows** Java conventions and quality standards
- [ ] **Tests written** and passing (unit + acceptance)
- [ ] **Build script** runs successfully
- [ ] **CodeScene analysis** shows no regressions
- [ ] **Documentation updated** if needed

### Testing Phase (`status::in-testing`)
- [ ] **Merge request** created with proper template
- [ ] **Review requested** from team members
- [ ] **All tests passing** in CI/CD pipeline
- [ ] **Code review feedback** addressed
- [ ] **Quality gates** satisfied

### Showcase Phase (`status::ready-for-showcase`)
- [ ] **Demo prepared** and tested
- [ ] **Acceptance criteria** fully met
- [ ] **Documentation** complete and reviewed
- [ ] **Stakeholder demo** scheduled
- [ ] **Feedback incorporated** from showcase

---

Thank you for contributing to Robot World! 🤖🌍

*Last Updated: 2025-06-29*
*Version: Iteration 1*
