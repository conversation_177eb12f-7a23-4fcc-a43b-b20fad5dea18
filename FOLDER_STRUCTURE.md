# Layered Architecture Folder Structure

## Created Folder Structure for Iteration 4 Refactoring

```
src/main/java/za/co/wethinkcode/robots/
├── presentation/                    # PRESENTATION LAYER
│   ├── controllers/                 # Request/Response handling
│   │   ├── NetworkController.java   # TCP connection management
│   │   ├── RobotController.java     # Robot command processing
│   │   └── AdminController.java     # Admin console commands
│   └── display/                     # Display/UI services
│       └── WorldDisplayService.java # World visualization (extracted from World.java)
│
├── service/                         # SERVICE LAYER
│   ├── GameService.java             # Main game orchestration
│   ├── WorldService.java            # World management operations
│   └── RobotService.java            # Robot-specific operations
│
├── dao/                            # DATA ACCESS LAYER
│   ├── WorldDAO.java               # Enhanced world operations interface
│   ├── RobotDAO.java               # Robot persistence interface
│   └── impl/                       # DAO implementations
│       ├── WorldDAOImpl.java       # Enhanced world DAO implementation
│       └── RobotDAOImpl.java       # Robot DAO implementation
│
├── infrastructure/                  # INFRASTRUCTURE LAYER
│   ├── networking/                  # Network-related infrastructure
│   │   ├── ServerManager.java       # Server lifecycle management
│   │   └── ConnectionManager.java   # Connection pooling
│   └── configuration/               # Configuration and DI
│       └── ApplicationContext.java  # Dependency injection container
│
├── api/                            # WEB API LAYER (Section 3)
│   ├── ApiServer.java              # Main API server (Javalin)
│   ├── controllers/                # REST API controllers
│   │   ├── WorldApiController.java  # GET /world, GET /world/{name}
│   │   └── RobotApiController.java  # POST /robot/{name}
│   └── config/                     # API configuration
│       └── ApiConfiguration.java   # API setup and routing
│
├── examples/                       # HTTP CLIENT EXAMPLES (Section 2)
│   └── RandomCatFact.java          # Java HTTP client using Unirest
│
└── [existing packages remain unchanged]
    ├── Robot.java                  # Domain model (clean)
    ├── commands/                   # Command pattern (clean)
    ├── handlers/                   # Command handlers (clean)
    ├── client/                     # Client-side code
    └── server/                     # Server domain objects
        ├── World.java              # Domain model (to be cleaned)
        ├── Response.java           # Response objects
        ├── Obstacle.java           # Domain objects
        └── db/                     # Existing DAO (to be moved/enhanced)
```

## Java Examples Structure

```
src/main/java/za/co/wethinkcode/robots/examples/
└── RandomCatFact.java              # Java HTTP client using Unirest
```

## Test Structure

```
src/test/java/za/co/wethinkcode/robots/
├── presentation/
│   └── controllers/                # Controller unit tests
│       ├── NetworkControllerTest.java
│       ├── RobotControllerTest.java
│       └── AdminControllerTest.java
│
├── service/                        # Service layer tests
│   ├── GameServiceTest.java
│   ├── WorldServiceTest.java
│   └── RobotServiceTest.java
│
├── dao/                           # DAO layer tests
│   ├── RobotDAOImplTest.java
│   └── EnhancedWorldDAOImplTest.java
│
└── api/                           # API integration tests
    ├── ApiServerTest.java
    ├── WorldApiControllerTest.java
    └── RobotApiControllerTest.java
```

## Files to be Created (Implementation Tasks)

### Presentation Layer
- [ ] `NetworkController.java` - Extract socket handling from Server.java
- [ ] `RobotController.java` - Extract command processing from Server.java  
- [ ] `AdminController.java` - Extract admin console from Server.java
- [ ] `WorldDisplayService.java` - Extract display logic from World.java

### Service Layer
- [ ] `GameService.java` - Main orchestration with DAO injection
- [ ] `WorldService.java` - World management operations
- [ ] `RobotService.java` - Robot-specific operations

### Enhanced DAO Layer
- [ ] `RobotDAO.java` - Robot persistence interface
- [ ] `RobotDAOImpl.java` - Robot DAO implementation
- [ ] Enhanced `WorldDAO.java` - Add new methods
- [ ] Enhanced `WorldDAOImpl.java` - Implement new methods

### Infrastructure Layer
- [ ] `ServerManager.java` - Server lifecycle and threading
- [ ] `ConnectionManager.java` - Connection management
- [ ] `ApplicationContext.java` - Dependency injection

### API Layer (Section 3)
- [ ] `ApiServer.java` - Javalin-based API server
- [ ] `WorldApiController.java` - World REST endpoints
- [ ] `RobotApiController.java` - Robot REST endpoints
- [ ] `ApiConfiguration.java` - API configuration

### Examples (Section 2)
- [ ] `RandomCatFact.java` - Java HTTP client with Unirest

### Test Files
- [ ] All corresponding test files for new classes
- [ ] Integration tests for API endpoints
- [ ] Enhanced tests for existing DAO classes

## Files to be Modified

### Domain Layer Cleanup
- [ ] `World.java` - Remove System.out.println, keep only domain logic
- [ ] `Server.java` - Refactor to use new layered architecture
- [ ] `DatabaseConnection.java` - Add robots table creation

### Configuration
- [ ] `pom.xml` - Add Javalin and Unirest dependencies

### Files to be Removed
- [ ] `ClientHandler.java` - Functionality moved to NetworkController

## Implementation Phases

1. **Phase 1**: Create presentation and service layers (Section 1)
2. **Phase 2**: Implement enhanced DAO pattern (Section 4)
3. **Phase 3**: Create Java HTTP client example (Section 2)
4. **Phase 4**: Implement Web API layer (Section 3)

## Benefits of This Structure

- **Clear Separation**: Each layer has distinct responsibilities
- **Testability**: Easy to unit test each layer independently
- **Maintainability**: Changes in one layer don't affect others
- **Scalability**: Easy to add new features in appropriate layers
- **Consistency**: Builds on existing DAO pattern
