package za.co.wethinkcode.robots.commands;

import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.Robot;
import za.co.wethinkcode.robots.server.Obstacle;
import za.co.wethinkcode.robots.server.ObstacleType;
import za.co.wethinkcode.robots.server.Response;
import za.co.wethinkcode.robots.server.World;
import java.util.concurrent.atomic.AtomicInteger;
import static org.junit.jupiter.api.Assertions.*;


public class CommandTests {

        @Test
        public void testValidCommands() {
            assertTrue(Command.isValidCommand("forward"));
            assertTrue(Command.isValidCommand("back"));
            assertTrue(Command.isValidCommand("turn"));
            assertTrue(Command.isValidCommand("look"));
            assertTrue(Command.isValidCommand("state"));
            assertTrue(Command.isValidCommand("launch"));
        }

        @Test
        public void testInvalidCommands() {
            assertFalse(Command.isValidCommand("teleport"));
            assertFalse(Command.isValidCommand("fly"));
            assertFalse(Command.isValidCommand("dance"));
            assertFalse(Command.isValidCommand("spin around"));
            assertFalse(Command.isValidCommand("l00k"));
        }


    @Test
    public void testLaunchTwoRobotsPerClientLimit() {
        Robot robot1 = new Robot("Alpha", "tank");
        World world = new World(10, 10);
        LaunchCommand command = new LaunchCommand(robot1, new String[]{"tank"});
        world.execute(command, response -> assertTrue(response.isOKResponse()));
    }

    @Test
    public void testLookCommand() {
        Robot robot1 = new Robot("Alpha", "tank");
        World world = new World(10, 10);
        LaunchCommand command = new LaunchCommand(robot1, new String[]{"tank"});

        world.execute(command, response -> {
            assertTrue(response.isOKResponse());
            Robot launchedRobot = world.getRobots().getFirst();

            world.execute(new LookCommand(launchedRobot, new String[]{}), lookResponse ->  {
               assertTrue(lookResponse.isOKResponse());
               assertNotEquals(0, lookResponse.object.getJSONObject("data").getJSONArray("objects").length(), "Should be able to set objects");
            });
        });
    }

    @Test
    public void testLookWithNoRobots() {
        Robot robot = new Robot("Alpha", "tank");
        World world = new World(10, 10);

        world.execute(new LookCommand(robot, new String[]{}), lookResponse -> {
            assertFalse(lookResponse.isOKResponse());
            assertEquals("Could not find robot: Alpha", lookResponse.getMessage());
        });
    }

    @Test
    public void testSuccessfulLaunch() {
        Robot robot1 = new Robot("Alpha", "tank");
        World world = new World(10, 10);
        LaunchCommand command = new LaunchCommand(robot1, new String[]{"tank"});

        world.execute(command, response -> assertTrue(response.isOKResponse()));
    }

    public void validateCommandMessage(Response response, boolean assertTrue, String expectedMessage) {
        if (assertTrue) {
            assertTrue(response.isOKResponse());
        } else {
            assertFalse(response.isOKResponse());
        }

        if (!expectedMessage.isEmpty()) {
            assertEquals(expectedMessage, response.getMessage());
        }
    }

    public void validateCommandMessage(Response response, String expectedMessage) {
        validateCommandMessage(response, true, expectedMessage);
    }

    @Test
    public void testOrientationCommand() {
        Robot robot1 = new Robot("Alpha", "tank");
        World world = new World(10, 10);
        LaunchCommand command = new LaunchCommand(robot1, new String[]{"tank"});

        world.execute(command, response -> {
            assertTrue(response.isOKResponse());
            Robot launchedRobot = world.getRobots().getFirst();

            OrientationCommand orientationCommand = new OrientationCommand(launchedRobot);

            world.execute(orientationCommand, orientationResponse -> {
                validateCommandMessage(orientationResponse, "Alpha is facing NORTH.");
            });
        });
    }

    @Test
    public void testHandleTurnLeft() {
      turnLeft(true);
    }

    @Test
    public void testHandleTurnRight() {
        turnLeft(false);
    }

    public void turnLeft(boolean left) {
        Robot robot1 = new Robot("Alpha", "tank");
        World world = new World(10, 10);
        LaunchCommand command = new LaunchCommand(robot1, new String[]{"tank"});

        world.execute(command, response -> {
            assertTrue(response.isOKResponse());
            Robot launchedRobot = world.getRobots().getFirst();

            TurnCommand turnCommand = new TurnCommand(launchedRobot, new String[]{left ? "left" : "right"});

            world.execute(turnCommand, turnResponse -> {
                validateCommandMessage(turnResponse, left ? "Alpha turned left to WEST" : "Alpha turned right to EAST");
                assertEquals(left ? "WEST" : "EAST", turnResponse.object.getJSONObject("state").getString("direction"));
            });
        });
    }

    @Test
    public void testMoveForwardIntoPit() {
        Robot robot1 = new Robot("Alpha", "tank");
        World world = new World(10, 10);

        world.addRobot(robot1);

        robot1.setPosition(0, 0);
        Obstacle pit = new Obstacle(ObstacleType.PIT, 0, 1, 1,1);

        world.addObstacle(pit);

        String[] args = new String[]{"1"};
        MoveCommand moveCommand = new MoveCommand(robot1, "forward", args);

        world.execute(moveCommand, moveResponse -> {
            assertTrue(moveResponse.getMessage().contains("Fell"));
            assertEquals(Robot.RobotStatus.Dead, world.getRobots().getFirst().status);
        });
    }

    @Test
    public void testMoveBack() {
        moveForward(false);
    }

    public void moveForward(boolean forward) {
        Robot robot1 = new Robot("Alpha", "tank");
        World world = new World(10, 10);
        LaunchCommand command = new LaunchCommand(robot1, new String[]{"tank"});

        world.execute(command, response -> {
            assertTrue(response.isOKResponse());
            Robot launchedRobot = world.getRobots().getFirst();

            launchedRobot.setPosition(0, 0);
            String[] args = new String[]{"1"};
            MoveCommand moveCommand = new MoveCommand(launchedRobot, forward ? "forward" : "back", args);

            world.execute(moveCommand, moveResponse -> {
                assertTrue(moveResponse.getMessage().contains("Done"));
            });
        });
    }

    @Test
    public void testMoveBackIntoPit() {
        Robot robot1 = new Robot("Alpha", "tank");
        World world = new World(10, 10);

        world.addRobot(robot1);

        robot1.setPosition(0, 0);
        Obstacle pit = new Obstacle(ObstacleType.PIT, 0, -1, 1,1);

        world.addObstacle(pit);

        String[] args = new String[]{"1"};
        MoveCommand moveCommand = new MoveCommand(robot1, "back", args);

        world.execute(moveCommand, moveResponse -> {
            assertTrue(moveResponse.getMessage().contains("Fell"));
            assertEquals(Robot.RobotStatus.Dead, world.getRobots().getFirst().status);
        });
    }

    @Test
    public void testStateCommand() {
        Robot robot1 = new Robot("Alpha", "tank");
        World world = new World(10, 10);
        LaunchCommand command = new LaunchCommand(robot1, new String[]{"tank"});

        world.execute(command, response -> {
            assertTrue(response.isOKResponse());
            Robot launchedRobot = world.getRobots().getFirst();

            StateCommand stateCommand = new StateCommand(launchedRobot, new String[]{});

            world.execute(stateCommand, stateResponse -> {
                validateCommandMessage(stateResponse, "");
                assertEquals("NORTH", stateResponse.object.getJSONObject("state").getString("direction"));
                assertEquals("NORMAL", stateResponse.object.getJSONObject("state").getString("status"));
            });
        });
    }

    @Test
    public void testFireMisses() {
        Robot robot1 = new Robot("Alpha", "tank");
        World world = new World(10, 10);
        LaunchCommand command = new LaunchCommand(robot1, new String[]{"tank"});

        world.execute(command, response -> {
            assertTrue(response.isOKResponse());
            Robot launchedRobot = world.getRobots().getFirst();

            FireCommand fireCommand = new FireCommand(launchedRobot, new String[]{});

            world.execute(fireCommand, fireResponse -> {
                validateCommandMessage(fireResponse, "Miss");
            });
        });
    }

    @Test
    public void testFireHits() {
        Robot shooter = new Robot("Alpha", "tank");
        Robot target = new Robot("Hal", "tank");
        World world = new World(10, 10);

        world.addRobot(shooter);
        world.addRobot(target);

        shooter.setPosition(0, 1);
        target.setPosition(0, 2);

        int shooterInitialShots = shooter.getShots();
        int targetInitialShield = target.getShields();

        world.displayWorld();

        FireCommand fireCommand = new FireCommand(shooter, new String[]{});

        world.execute(fireCommand, fireResponse -> {
            validFireState(target, fireResponse, shooterInitialShots, targetInitialShield);
        });
    }

    @Test
    public void testFireHitsWithSniper() {
        Robot shooter = new Robot("Alpha", "sniper");
        Robot target = new Robot("Hal", "tank");
        World world = new World(10, 10);

        world.addRobot(shooter);
        world.addRobot(target);

        shooter.setPosition(0, 1);
        target.setPosition(0, 2);

        int shooterInitialShots = shooter.getShots();
        int targetInitialShield = target.getShields();

        world.displayWorld();

        FireCommand fireCommand = new FireCommand(shooter, new String[]{});

        world.execute(fireCommand, fireResponse -> {
            validFireState(target, fireResponse, shooterInitialShots, targetInitialShield);
        });
    }

    public void validFireState(Robot target, Response response, int shooterInitialShots, int targetInitialShield) {
        assertTrue(response.isOKResponse());
        assertEquals(shooterInitialShots - 1, response.object.getJSONObject("state").getInt("shots"));
        assertEquals(targetInitialShield - 1, response.object.getJSONObject("data").getJSONObject("state").getInt("shields"));
        assertEquals(target.getName(), response.object.getJSONObject("data").getString("robot"));
    }

    @Test
    public void testFireKills() {
        Robot shooter = new Robot("Alpha", "tank");
        Robot target = new Robot("Hal", "tank");
        World world = new World(10, 10);

        world.addRobot(shooter);
        world.addRobot(target);

        shooter.setPosition(0, 1);
        target.setPosition(0, 2);
        target.setShields(0);

        int shooterInitialShots = shooter.getShots();

        world.displayWorld();

        FireCommand fireCommand = new FireCommand(shooter, new String[]{});

        world.execute(fireCommand, fireResponse -> {
            assertEquals(0, fireResponse.object.getJSONObject("data").getJSONObject("state").getInt("shields"));
            assertEquals("DEAD", fireResponse.object.getJSONObject("data").getJSONObject("state").getString("status"));
        });
    }

    @Test
    public void testReloading() {
        Robot robot = new Robot("Alpha", "tank");
        World world = new World(10, 10);

        world.addRobot(robot);
        int shooterInitialShots = robot.getShots();

        FireCommand fireCommand = new FireCommand(robot, new String[]{});

        world.execute(fireCommand, fireResponse -> {
            assertTrue(fireResponse.isOKResponse());
            assertEquals(shooterInitialShots - 1, robot.getShots());

            ReloadCommand reloadCommand = new ReloadCommand(robot, new  String[]{});
            AtomicInteger invocations = new AtomicInteger(0);

            world.execute(reloadCommand, reloadResponse -> {
                invocations.getAndIncrement();
                assertTrue(reloadResponse.isOKResponse());

                if (invocations.get() == 1) {
                    validateCommandMessage(reloadResponse, "Alpha is now reloading.");
                } else {
                    validateCommandMessage(reloadResponse, "Alpha is done.");
                    assertEquals(shooterInitialShots, reloadResponse.object.getJSONObject("state").getInt("shots"));
                }
            });
        });
    }

    @Test
    public void testRepairing() {
        Robot robot = new Robot("Alpha", "tank");
        World world = new World(10, 10);

        world.addRobot(robot);
        int robotInitialShields = robot.getShields();

        robot.takeHit();
        RepairCommand repairCommand = new RepairCommand(robot, new String[]{});

        world.execute(repairCommand, repairResponse -> {
            assertTrue(repairResponse.isOKResponse());

            if (repairResponse.isOKResponse() && repairResponse.getMessage().equalsIgnoreCase("Alpha is now repairing.")) {
                assertNotEquals(robotInitialShields, robot.getShields());
            } else if (repairResponse.isOKResponse()) {
//                assertEquals(robotInitialShields, robot.getShields());
            }
        });
    }

    @Test
    public void testHelpCommand() {
        World world = new World(10, 10);

        HelpCommand helpCommand = new HelpCommand(null, null);

        world.execute(helpCommand, helpResponse -> {
            assertTrue(helpResponse.isOKResponse());
            assertTrue(helpResponse.getMessage().contains("I CAN UNDERSTAND THESE COMMANDS"));
        });
    }

    @Test
    public void testDumpCommand() {
        World world = new World(10, 10);

        DisconnectCommand disconnectCommand = new DisconnectCommand();

        world.execute(disconnectCommand, disconnectResponse -> assertTrue(disconnectResponse.isOKResponse()));
    }

    @Test
    public void testShutdown() {
        Robot robot = new Robot("Alpha", "tank");
        World world = new World(10, 10);

        world.addRobot(robot);

        ShutdownCommand shutdownCommand = new ShutdownCommand(robot, new String[]{});

        world.execute(shutdownCommand, shutdownResponse -> {
            validateCommandMessage(shutdownResponse, "Removed robot Alpha from the world.");
        });
    }

    @Test
    public void testStateCommandWithAllFields() {
        Robot robot1 = new Robot("Alpha", "tank");
        World world = new World(10, 10);
        LaunchCommand command = new LaunchCommand(robot1, new String[]{"tank"});

        world.execute(command, response -> {
            assertTrue(response.isOKResponse());
            Robot launchedRobot = world.getRobots().getFirst();

            StateCommand stateCommand = new StateCommand(launchedRobot, new String[]{});

            world.execute(stateCommand, stateResponse -> {
                validateCommandMessage(stateResponse, "");

                assertEquals("NORTH", stateResponse.object.getJSONObject("state").getString("direction"));
                assertEquals("NORMAL", stateResponse.object.getJSONObject("state").getString("status"));

                int x = stateResponse.object.getJSONObject("state").getJSONArray("position").getInt(0);
                int y = stateResponse.object.getJSONObject("state").getJSONArray("position").getInt(1);
                assertEquals(0, x);
                assertEquals(0, y);

                int shields = stateResponse.object.getJSONObject("state").getInt("shields");
                int shots = stateResponse.object.getJSONObject("state").getInt("shots");

                assertTrue(shields > 0);
                assertTrue(shots > 0);
            });
        });
    }

    @Test
    public void testStateShieldsCountAfterGettingHit() {
        Robot shooterBot = new Robot("Shooter", "tank");
        Robot targetBot = new Robot("Target", "tank");
        World world = new World(10, 10);

        world.addRobot(shooterBot);
        world.addRobot(targetBot);

        shooterBot.setPosition(0, 1);
        targetBot.setPosition(0, 2);
        int initialShields = targetBot.getShields();

        FireCommand fireCommand = new FireCommand(shooterBot, new String[]{});
        world.execute(fireCommand, fireResponse -> {
            validateCommandMessage(fireResponse, "");

            StateCommand stateCommand = new StateCommand(targetBot, new String[]{});
            world.execute(stateCommand, stateResponse -> {
                validateCommandMessage(stateResponse, "");
                int currentShields = stateResponse.object.getJSONObject("state").getInt("shields");
                assertEquals(initialShields - 1, currentShields);
            });
        });
    }

    @Test
    public void testLookReturnsEdgeWhenInRange() {
        Robot robot = new Robot("Alpha", "tank");
        World world = new World(5, 5);
        LaunchCommand launch = new LaunchCommand(robot, new String[]{"tank"});

        world.execute(launch, launchResponse -> {
            assertTrue(launchResponse.isOKResponse());
            Robot launchedRobot = world.getRobots().getFirst();

            launchedRobot.setPosition(0, 0);

            world.execute(new LookCommand(launchedRobot, new String[]{}), lookResponse -> {
                assertTrue(lookResponse.isOKResponse());
                var objects = lookResponse.object.getJSONObject("data").getJSONArray("objects");

                boolean foundEdge = false;

                for (int i = 0; i < objects.length(); i++) {
                    var obj = objects.getJSONObject(i);
                    if ("EDGE".equals(obj.getString("type"))) {
                        foundEdge = true;
                        int distance = obj.getInt("distance");

                        assertTrue(distance <= world.getVisibility());
                    }
                }

                assertTrue(foundEdge, "Should detect world edge within visibility range");
            });
        });
    }

}