package za.co.wethinkcode.robots.commands;

import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.Robot;
import za.co.wethinkcode.robots.server.Obstacle;
import za.co.wethinkcode.robots.server.ObstacleType;
import za.co.wethinkcode.robots.server.World;

import static org.junit.jupiter.api.Assertions.*;

public class CommandJsonTests {

    private Robot createTankRobot(String Robotname) {
        return new Robot(Robotname, "tank");
    }

    private Robot createSniperRobot(String Robotname) {
        return new Robot(Robotname, "sniper");
    }

    private World createWorld(int WorldSize) {
        return new World(WorldSize, WorldSize);
    }

    private void ProtocolStateJsonResponse(JSONObject state) {
        assertNotNull(state);
        assertTrue(state.has("position"));
        assertEquals(2, state.getJSONArray("position").length());
        assertTrue(state.has("direction"));
        assertTrue(state.has("shields"));
        assertTrue(state.has("shots"));
        assertTrue(state.has("status"));
    }

    private void OKResponse(JSONObject response) {
        assertEquals("OK", response.getString("result"));
        assertTrue(response.has("data"));
    }

    private void ErrorWithMessage(JSONObject response, String expectedMessage) {
        assertEquals("ERROR", response.getString("result"));
        assertTrue(response.has("data"));
        assertTrue(response.getJSONObject("data").getString("message").contains(expectedMessage));
    }

    private void executeAndAssertFireResult(World world, Robot shooter, Robot target, int shooterX, int shooterY, int targetX, int targetY, String expectedMsg) {
        world.addRobot(shooter);
        world.addRobot(target);
        shooter.setPosition(shooterX, shooterY);
        target.setPosition(targetX, targetY);

        FireCommand fireCommand = new FireCommand(shooter, new String[]{});
        world.execute(fireCommand, response -> {
            JSONObject jsonResponse = response.object;
            OKResponse(jsonResponse);
            JSONObject data = jsonResponse.getJSONObject("data");

            assertEquals(expectedMsg, data.getString("message"));
            assertTrue(data.has("distance"));
            assertTrue(data.has("robot"));
            assertTrue(data.has("state"));

            JSONObject state = jsonResponse.getJSONObject("state");
            assertTrue(state.has("shots"));
        });
    }

    private void assertStandardState(JSONObject state) {
        assertNotNull(state);
        assertTrue(state.has("direction"));
        assertTrue(state.has("position"));
        assertTrue(state.has("shields"));
        assertTrue(state.has("shots"));
        assertTrue(state.has("status"));
    }

    @Test
    public void testSuccessfulLaunchCommandJsonResponse() {
        Robot robot = createTankRobot("Alpha");
        World world = createWorld(3);
        LaunchCommand launchCommand = new LaunchCommand(robot, new String[]{"tank"});

        // Execute the command with a completion handler
        world.execute(launchCommand, response -> {

            JSONObject jsonResponse = response.object;

            OKResponse(jsonResponse);
            ProtocolStateJsonResponse(jsonResponse.getJSONObject("state"));

            JSONObject state = jsonResponse.getJSONObject("state");

            assertEquals("NORTH", state.getString("direction"));
            assertEquals("NORMAL", state.getString("status"));
        });
    }

    @Test
    public void testLaunchCommandNoFreeLocationJsonResponse() {
        Robot robot = createTankRobot("Alpha");
        Robot robot2 = createSniperRobot("Beta");
        World world = createWorld(1);
        world.addRobot(robot2);
        LaunchCommand launchCommand = new LaunchCommand(robot, new String[]{"tank"});

        world.execute(launchCommand, response -> {
            JSONObject jsonResponse = response.object;
            ErrorWithMessage(jsonResponse,"No more space in this world");
        });
    }

    @Test
    public void testLaunchCommandNameExistsJsonResponse() {
        Robot robot2 = createSniperRobot("Alpha");
        World world = createWorld(2);
        world.addRobot(robot2);
        LaunchCommand launchCommand = new LaunchCommand(createTankRobot("Alpha"), new String[]{"tank"});

        world.execute(launchCommand, response -> {
            JSONObject jsonResponse = response.object;

            ErrorWithMessage(jsonResponse,"Too many of you in this world");
        });
    }

    @Test
    public void testSuccessfulLookCommandJsonResponse() {
        Robot robot = createTankRobot("Alpha");
        World world = createWorld(10);
        world.addRobot(robot);

        LookCommand lookCommand = new LookCommand(robot, new String[]{"tank"});

        world.execute(lookCommand, response -> {
            JSONObject jsonResponse = response.object;

            OKResponse(jsonResponse);
            assertTrue(jsonResponse.getJSONObject("data").getJSONArray("objects").length() >= 0);
            if (!jsonResponse.getJSONObject("data").getJSONArray("objects").isEmpty()) {
                assertTrue(jsonResponse.getJSONObject("data").getJSONArray("objects").getJSONObject(1).has("type"));
                assertTrue(jsonResponse.getJSONObject("data").getJSONArray("objects").getJSONObject(0).has("direction"));
                assertTrue(jsonResponse.getJSONObject("data").getJSONArray("objects").getJSONObject(2).has("distance"));
            }
            assertTrue(jsonResponse.has("state"));
        });
    }

    @Test
    public void testStateCommandJsonResponse(){
        Robot robot = createTankRobot("Alpha");
        World world = createWorld(10);
        LaunchCommand launchCommand = new LaunchCommand(robot, new String[]{"tank"});

        world.execute(launchCommand, response -> {
           assertTrue(response.isOKResponse());
           Robot launchedRobot = world.getRobots().getFirst();

           StateCommand stateCommand = new StateCommand(launchedRobot, new String[]{});
           world.execute(stateCommand, stateResponse -> {
               JSONObject jsonResponse = response.object;

               OKResponse(jsonResponse);
               assertTrue(jsonResponse.getJSONObject("data").isEmpty());
               ProtocolStateJsonResponse(jsonResponse.getJSONObject("state"));
           });
        });
    }

    @Test
    public void testFireCommandJsonResponse(){
        executeAndAssertFireResult(createWorld(10),
                createTankRobot("Shooter"), createTankRobot("Target"),
                0, 1, 0, 2, "Hit");
    }

    @Test
    public void testFireCommandMissJsonResponse(){
        Robot shooterBot = createTankRobot("Shooter");
        Robot targetBot = createTankRobot("Target");
        World world = createWorld(10);

        world.addRobot(shooterBot);
        world.addRobot(targetBot);

        shooterBot.setPosition(0, 0);
        targetBot.setPosition(1, 1);

        FireCommand fireCommand = new FireCommand(shooterBot, new String[]{});
        world.execute(fireCommand, fireResponse -> {
            JSONObject jsonResponse =fireResponse.object;

           OKResponse(jsonResponse);

            JSONObject data = jsonResponse.getJSONObject("data");
            assertNotNull(data);

            assertEquals("Miss", data.getString("message"));

            assertTrue(jsonResponse.has("state"));
            JSONObject state = jsonResponse.getJSONObject("state");
            assertNotNull(state);

            assertTrue(state.has("shots"));
        });
    }

    @Test
    public void testMovementForwardOKResponse() {
        Robot robot = new Robot("Hal");
        World world = new World(21);

        world.addRobot(robot);

        String[] args = new String[]{"1"};
        MoveCommand moveCommand = new MoveCommand(robot, "forward", args);

        world.execute(moveCommand, response -> {
            assertTrue(response.isOKResponse());
            assertTrue(response.getMessage().equalsIgnoreCase("Done"));
            int y = response.object.getJSONObject("state").getJSONArray("position").getInt(1);

            assertEquals(1, y, "Robot should have moved");
        });
    }

    @Test
    public void testMovementBackwardOKResponse() {
        Robot robot = new Robot("Hal");
        World world = new World(21);

        world.addRobot(robot);

        String[] args = new String[]{"1"};
        MoveCommand moveCommand = new MoveCommand(robot, "back", args);

        world.execute(moveCommand, response -> {
            int y = response.object.getJSONObject("state").getJSONArray("position").getInt(1);

            assertTrue(response.isOKResponse());
            assertTrue(response.getMessage().equalsIgnoreCase("Done"));
            assertEquals(-1, y, "Robot should have moved");
        });
    }

    @Test
    public void testMovementObstacleObstructedResponse() {
        Robot robot = new Robot("Hal");
        World world = new World(21);

        world.addRobot(robot);
        Obstacle obstacle = new Obstacle(ObstacleType.MOUNTAIN, 0, 4, 1,1);
        world.addObstacle(obstacle);

        world.displayWorld();

        String[] args = new String[]{"4"};
        MoveCommand moveCommand = new MoveCommand(robot, "forward", args);

        world.execute(moveCommand, response -> {
            int y = response.object.getJSONObject("state").getJSONArray("position").getInt(1);

            assertTrue(response.isOKResponse());
            assertTrue(response.getMessage().equalsIgnoreCase("Obstructed"));
            assertEquals(3, y, "The position should be the last possible step the robot was able to take before it was obstructed.");
        });
    }

    @Test
    public void testMovementObstacleFellResponse() {
        Robot robot = new Robot("Hal");
        World world = new World(21);

        world.addRobot(robot);
        Obstacle obstacle = new Obstacle(ObstacleType.PIT, 0, 4, 1,1);
        world.addObstacle(obstacle);

        world.displayWorld();

        String[] args = new String[]{"4"};
        MoveCommand moveCommand = new MoveCommand(robot, "forward", args);

        world.execute(moveCommand, response -> {
            int y = response.object.getJSONObject("state").getJSONArray("position").getInt(1);
            String status = response.object.getJSONObject("state").getString("status");

            assertTrue(response.isOKResponse());
            assertTrue(response.getMessage().equalsIgnoreCase("Fell"));
            assertEquals("DEAD", status);
            assertEquals(4, y, "The robot should now be in the pit");
        });
    }


    @Test
    public void testSuccessfulTurnCommandJsonResponse() {
        Robot robot = createTankRobot("Alpha");
        World world = createWorld(3);
        world.addRobot(robot);

        // Test both left and right turns
        testTurnDirection(world, robot, "left");
        testTurnDirection(world, robot, "right");
    }



    private void testTurnDirection(World world, Robot robot, String direction) {
        TurnCommand turnCommand = new TurnCommand(robot, new String[]{direction});
        world.execute(turnCommand, response -> {
            JSONObject jsonResponse = response.object;
            OKResponse(jsonResponse);

            // Updated to check for the actual message format
            String message = jsonResponse.getJSONObject("data").getString("message");
            assertTrue(message.contains("turned " + direction));
            assertTrue(message.contains(robot.getName()));
            assertTrue(message.matches(".*(NORTH|SOUTH|EAST|WEST)"));


            JSONObject state = jsonResponse.getJSONObject("state");
            String newDirection = state.getString("direction");
            assertTrue(newDirection.matches("NORTH|SOUTH|EAST|WEST"));

            // Verify other standard state fields
            assertStandardState(jsonResponse.getJSONObject("state"));
        });
    }

    @Test
    public void testInvalidTurnDirectionJsonResponse() {
        Robot robot = createTankRobot("Alpha");
        World world = createWorld(3);
        world.addRobot(robot);

        TurnCommand turnCommand = new TurnCommand(robot, new String[]{"invalid"});

        world.execute(turnCommand, response -> {
            JSONObject jsonResponse = response.object;

            ErrorWithMessage(jsonResponse,"Invalid direction");
        });
    }


}




