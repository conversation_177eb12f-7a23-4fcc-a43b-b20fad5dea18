package za.co.wethinkcode.robots;

import org.junit.jupiter.api.extension.AfterEachCallback;
import org.junit.jupiter.api.extension.BeforeEachCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.io.IOException;

public class ServerExtension implements BeforeEachCallback, AfterEachCallback {
    private Process serverProcess;
    private Thread serverThread;
    private Boolean usingOwnServer = false;
    private Boolean debug = false;
    private Boolean useDocker = false;
    private String dockerContainerName = null;
    private static int serverPort = 5000; // Default port

    @Override
public void beforeEach(ExtensionContext context) throws Exception {
    ServerConfig config = context.getRequiredTestMethod().getAnnotation(ServerConfig.class);
    String jarPath = "libs/robot-worlds-server-0.2.0.jar";
    String useOwnServer = System.getenv("USE_OWN_SERVER");
    String debugMode = System.getenv("DEBUG");
    String dockerMode = System.getenv("USE_DOCKER");
    String serverPortEnv = System.getenv("SERVER_PORT");

    boolean isDockerTestMode = isDockerTestMode(useOwnServer, serverPortEnv);

    if (isDockerTestMode) {
        handleDockerTestMode(config, serverPortEnv);
        return;
    }

    setServerPortFromEnv(serverPortEnv);

    if (isUsingOwnServer(useOwnServer)) {
        usingOwnServer = true;
        handleOwnServer(config, serverPortEnv);
        return;
    }

    setDebugMode(debugMode);
    setDockerMode(dockerMode);

    if (useDocker) {
        startDockerServer(config);
    } else {
        startJarServer(config, jarPath);
    }

    waitForServerOrThrow(config);
}

private boolean isDockerTestMode(String useOwnServer, String serverPortEnv) {
    // Check if we're in Docker test mode (when USE_OWN_SERVER is true and SERVER_PORT is set)
    return "true".equalsIgnoreCase(useOwnServer) && serverPortEnv != null;
}

private void setServerPortFromEnv(String serverPortEnv) {
    // Set the server port from environment variable if provided
    if (serverPortEnv != null && !serverPortEnv.isEmpty()) {
        try {
            serverPort = Integer.parseInt(serverPortEnv);
        } catch (NumberFormatException e) {
            System.err.println("Invalid SERVER_PORT value: " + serverPortEnv + ", using default port 5000");
            serverPort = 5000;
        }
    }
}

private boolean isUsingOwnServer(String useOwnServer) {
    return "true".equalsIgnoreCase(useOwnServer);
}

private void handleOwnServer(ServerConfig config, String serverPortEnv) {
    // When using own server, still wait for it to be ready on the configured port
    String[] args = config != null ? config.arguments() : new String[0];
    int port = serverPortEnv != null ? serverPort : extractPort(args);
    boolean ready = waitForServer("localhost", port);

    if (!ready) {
        throw new RuntimeException("External server is not ready on port " + port + " within timeout.");
    }
}

private void setDebugMode(String debugMode) {
    if ("true".equalsIgnoreCase(debugMode)) {
        debug = true;
    }
}

private void setDockerMode(String dockerMode) {
    if ("true".equalsIgnoreCase(dockerMode)) {
        useDocker = true;
    }
}

private void waitForServerOrThrow(ServerConfig config) {
    // Wait for server with a timeout
    String[] args = config != null ? config.arguments() : new String[0];
    int port = 5000;
    boolean ready = waitForServer("localhost", port);

    if (!ready) {
        throw new RuntimeException("Server failed to start on port " + port + " within timeout.");
    }
}

private void handleDockerTestMode(ServerConfig config, String serverPortEnv) throws Exception {
    useDocker = true;
    try {
        serverPort = Integer.parseInt(serverPortEnv);
    } catch (NumberFormatException e) {
        System.err.println("Invalid SERVER_PORT value: " + serverPortEnv + ", using default port 5050");
        serverPort = 5050;
    }

    startDockerServer(config);

    // Wait for server with a timeout
    String[] args = config != null ? config.arguments() : new String[0];
    int port = extractPort(args);
    if (port == 5000) { // If no port specified in config, use the environment port
        port = serverPort;
    }
    boolean ready = waitForServer("localhost", port);

    if (!ready) {
        throw new RuntimeException("Docker server failed to start on port " + port + " within timeout.");
    }
}

private void startJarServer(ServerConfig config, String jarPath) {
    List<String> command = new ArrayList<>();
    buildCommand(command, config, jarPath);

    ProcessBuilder pb = new ProcessBuilder(command);
    pb.redirectOutput(ProcessBuilder.Redirect.INHERIT);
    pb.redirectError(ProcessBuilder.Redirect.INHERIT);

    // Start server in a separate thread
    serverThread = new Thread(() -> {
        try {
            serverProcess = pb.start();
            serverProcess.waitFor();
        } catch (IOException | InterruptedException e) {
            throw new RuntimeException("Failed to start or monitor server process", e);
        }
    });

    serverThread.start();
}

private void startDockerServer(ServerConfig config) throws IOException, InterruptedException {
    String[] args = config != null ? config.arguments() : new String[0];
    int configPort = extractPort(args);

    // Use dynamic port allocation to avoid conflicts
    int port = findAvailablePort();
    serverPort = port; // Update the server port for this test

    // Generate unique container name for this test
    dockerContainerName = "robot-test-" + System.currentTimeMillis();

    // Build Docker command
    List<String> dockerCommand = new ArrayList<>();
    dockerCommand.add("docker");
    dockerCommand.add("run");
    dockerCommand.add("-d");
    dockerCommand.add("-p");
    dockerCommand.add(port + ":" + port);
    dockerCommand.add("--name");
    dockerCommand.add(dockerContainerName);
    dockerCommand.add("robot-worlds-server:0.2.0");

    // Add server arguments - always include port
    dockerCommand.add("-p");
    dockerCommand.add(String.valueOf(port));

    // Add other server arguments from config
    if (config != null && config.arguments().length > 0) {
        // Filter out -p arguments since we already added the port
        List<String> filteredArgs = new ArrayList<>();
        for (int i = 0; i < config.arguments().length; i++) {
            if ("-p".equals(config.arguments()[i])) {
                i++; // Skip the next argument (port value) as well
            } else {
                filteredArgs.add(config.arguments()[i]);
            }
        }
        dockerCommand.addAll(filteredArgs);
        System.out.println("Starting Docker server with custom args: " + Arrays.toString(config.arguments()) + " on port " + port);
    } else {
        System.out.println("Starting Docker server with default configuration on port " + port);
    }

    // Start Docker container
    ProcessBuilder pb = new ProcessBuilder(dockerCommand);
    pb.redirectOutput(ProcessBuilder.Redirect.INHERIT);
    pb.redirectError(ProcessBuilder.Redirect.INHERIT);

    Process dockerProcess = pb.start();
    int exitCode = dockerProcess.waitFor();

    if (exitCode != 0) {
        throw new RuntimeException("Failed to start Docker container. Exit code: " + exitCode);
    }

    // Give container time to start
    Thread.sleep(5000);
}

private int findAvailablePort() throws IOException {
    // Start from port 5051 to avoid conflict with the main test container on 5050
    for (int port = 5051; port <= 6000; port++) {
        try (ServerSocket socket = new ServerSocket(port)) {
            return port;
        } catch (IOException e) {
            // Port is in use, try next one
        }
    }
    throw new IOException("No available ports found in range 5051-6000");
}

private void buildCommand(List<String> command, ServerConfig config, String jarPath) {
    // When running for IntelliJ we're more than likely debugging stuff with our server
    if (debug) {
        buildMavenCommand(command, config);
    } else {
        buildJarCommand(command, config, jarPath);
    }
}

private void buildMavenCommand(List<String> command, ServerConfig config) {
    // Run mvn exec:java instead of java -jar
    // for our server
    // once we start packing our server we can use -jar
    command.add("mvn");
    command.add("exec:java");
    command.add("-Dexec.mainClass=za.co.wethinkcode.robots.server.Server");

    String execArgs = getExecArgs(config);
    command.add("-Dexec.args=" + execArgs);
}

private String getExecArgs(ServerConfig config) {
    String execArgs = "";
    if (config != null && config.arguments().length > 0) {
        execArgs = String.join(" ", config.arguments());
        System.out.println("Starting server with custom args: " + execArgs);
    } else {
        execArgs = "-s 1";
        System.out.println("Starting server with default configuration.");
    }
    return execArgs;
}

private void buildJarCommand(List<String> command, ServerConfig config, String jarPath) {
    command.add("java");
    command.add("-jar");
    command.add(jarPath);

    if (config != null && config.arguments().length > 0) {
        command.addAll(Arrays.asList(config.arguments()));
        System.out.println("Starting server with custom args: " + command.subList(3, command.size()));
    } else {
        // Default configuration
        System.out.println("Starting server with default configuration.");
    }
}

    @Override
    public void afterEach(ExtensionContext context) throws InterruptedException {
        // Add a small delay to ensure test method completes before cleanup
        Thread.sleep(1000);

        // Always clean up Docker containers in Docker test mode
        if (useDocker && dockerContainerName != null) {
            stopDockerServer();
        } else if (!usingOwnServer) {
            // Only clean up JAR servers if not using external server
            if (serverProcess != null) {
                stopJarServer();
            }

            // Interrupt server thread if still running
            if (serverThread != null && serverThread.isAlive()) {
                serverThread.interrupt();
            }
        }

        // Reset state for next test
        useDocker = false;
        usingOwnServer = false;
        dockerContainerName = null;
        serverProcess = null;
        serverThread = null;
    }

    private void stopJarServer() throws InterruptedException {
        // Try graceful shutdown first
        serverProcess.destroy();

        // Wait up to 3 seconds for graceful shutdown
        boolean terminated = serverProcess.waitFor(3, java.util.concurrent.TimeUnit.SECONDS);

        if (!terminated) {
            serverProcess.destroyForcibly();
            // Wait for forced termination
            serverProcess.waitFor(2, java.util.concurrent.TimeUnit.SECONDS);
        }

        // Additional cleanup time to ensure port is released
        Thread.sleep(3000);
    }

    private void stopDockerServer() {
        if (dockerContainerName == null) {
            return;
        }

        try {
            System.out.println("Stopping Docker container: " + dockerContainerName);

            // Force stop the container (in case it's not responding)
            ProcessBuilder stopPb = new ProcessBuilder("docker", "stop", "-t", "5", dockerContainerName);
            stopPb.redirectOutput(ProcessBuilder.Redirect.INHERIT);
            stopPb.redirectError(ProcessBuilder.Redirect.INHERIT);
            Process stopProcess = stopPb.start();
            int stopExitCode = stopProcess.waitFor();

            if (stopExitCode != 0) {
                System.err.println("Warning: docker stop returned exit code " + stopExitCode);
                // Try to force kill the container
                ProcessBuilder killPb = new ProcessBuilder("docker", "kill", dockerContainerName);
                Process killProcess = killPb.start();
                killProcess.waitFor(5, java.util.concurrent.TimeUnit.SECONDS);
            }

            // Remove the container
            ProcessBuilder rmPb = new ProcessBuilder("docker", "rm", "-f", dockerContainerName);
            rmPb.redirectOutput(ProcessBuilder.Redirect.INHERIT);
            rmPb.redirectError(ProcessBuilder.Redirect.INHERIT);
            Process rmProcess = rmPb.start();
            int rmExitCode = rmProcess.waitFor();

            if (rmExitCode == 0) {
                System.out.println("Docker container cleaned up: " + dockerContainerName);
            } else {
                System.err.println("Warning: docker rm returned exit code " + rmExitCode);
            }

            // Additional cleanup time to ensure port is released
            Thread.sleep(3000);

        } catch (IOException | InterruptedException e) {
            System.err.println("Error cleaning up Docker container: " + e.getMessage());
            e.printStackTrace();
        } finally {
            dockerContainerName = null;
        }
    }

    private static boolean isRunningFromIntelliJ() {
        return System.getProperty("intellij.debug.agent") != null ||
                System.getProperty("sun.java.command", "").contains("JUnitStarter");
    }

    private int extractPort(String[] args) {
        for (int i = 0; i < args.length - 1; i++) {
            if ("-p".equals(args[i])) {
                try {
                    return Integer.parseInt(args[i + 1]);
                } catch (NumberFormatException e) {
                    System.err.println("Invalid port number after -p: " + args[i + 1]);
                    break;
                }
            }
        }
        return 5000;
    }

    private boolean waitForServer(String host, int port) {
        long start = System.currentTimeMillis();
        int maxRetries = 25;
        int retryCount = 0;

        while (System.currentTimeMillis() - start < 10000 && retryCount < maxRetries) {
            try (Socket socket = new Socket()) {
                socket.connect(new InetSocketAddress(host, port), 1500);

                // Additional check: wait a bit more to ensure server is fully ready
                Thread.sleep(700);

                // Try a second connection to make sure server is stable
                try (Socket verifySocket = new Socket()) {
                    verifySocket.connect(new InetSocketAddress(host, port), 1000);
                    return true;
                }
            } catch (IOException ignored) {
                retryCount++;
                try {
                    Thread.sleep(1000); // Increased retry interval
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return false;
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
        }

        return false;
    }

    /**
     * Get the current server port being used for testing.
     * This allows tests to connect to the correct port whether using
     * an external server or a test-managed server.
     */
    public static int getServerPort() {
        // In Docker test mode, return the actual dynamic port being used
        return serverPort;
    }
}
