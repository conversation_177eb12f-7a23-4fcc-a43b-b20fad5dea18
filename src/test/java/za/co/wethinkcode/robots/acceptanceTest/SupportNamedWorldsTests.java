package za.co.wethinkcode.robots.acceptanceTest;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.*;
import za.co.wethinkcode.robots.server.Server;
import za.co.wethinkcode.robots.server.db.DatabaseConnection;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.Socket;
import java.sql.Connection;
import java.sql.SQLException;

import static org.junit.jupiter.api.Assertions.*;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class SupportNamedWorldsTests {
    private static Server server;
    private static Thread serverThread;
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final int PORT = 5000;


    @BeforeAll
    static void setUp() throws SQLException {
        DatabaseConnection.initializeDatabase();
        server = new Server(PORT, 10, null, true); // Enable test mode
        serverThread = new Thread(server::start);
        serverThread.start();
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @AfterAll
    static void tearDown() {
        Server.shutdown();
        try {
            serverThread.join(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    private void clearDatabase() throws SQLException {
        try (Connection conn = DatabaseConnection.getConnection()) {
            conn.prepareStatement("DELETE FROM obstacles").executeUpdate();
            conn.prepareStatement("DELETE FROM worlds").executeUpdate();
        }
    }

    /**
     * Given a running server
     */
    @Test
    @Order(1)
    void shouldSaveWorldByName() {
        try (Socket socket = new Socket("localhost", PORT);
             PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
             BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()))) {

            clearDatabase();

            // When I send the SAVE command with world name "world-1"
            String saveCommand = "{ \"robot\": \"A\", \"command\": \"save\", \"arguments\": [\"world-1\"] }";
            out.println(saveCommand);
            String responseStr = in.readLine();
            System.out.println("Save Response: " + responseStr);
            JsonNode response = objectMapper.readTree(responseStr);

            // Then the response should confirm successful saving
            assertEquals("OK", response.get("result").asText());
            assertTrue(response.get("data").get("message").asText().contains("saved"));
        } catch (Exception e) {
            fail("Exception during test: " + e.getMessage());
        }
    }

    /**
     * Given that "world-1" was previously saved
     */
    @Test
    @Order(2)
    void shouldRestoreWorldByName() {
        try (Socket socket = new Socket("localhost", PORT);
             PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
             BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()))) {

            // When I send the RESTORE command for "world-1"
            String restoreCommand = "{ \"robot\": \"A\", \"command\": \"restore\", \"arguments\": [\"world-1\"] }";
            out.println(restoreCommand);
            String responseStr = in.readLine();
            JsonNode response = objectMapper.readTree(responseStr);

            // Then the response should confirm successful restoration
            assertEquals("OK", response.get("result").asText());
            assertTrue(response.get("data").get("message").asText().contains("restored"));
        } catch (Exception e) {
            fail("Test failed due to exception: " + e.getMessage());
        }
    }

    /**
     * Given a world "world-1" already exists
     */
    @Test
    @Order(3)
    void shouldRefuseSaveIfWorldAlreadyExistsAndNoForce() {
        try (Socket socket = new Socket("localhost", PORT);
             PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
             BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()))) {

            // When I try to save it again without --force
            String saveAgainCommand = "{ \"robot\": \"A\", \"command\": \"save\", \"arguments\": [\"world-1\"] }";
            out.println(saveAgainCommand);
            String responseStr = in.readLine();
            System.out.println("Save Response: " + responseStr);
            JsonNode response = objectMapper.readTree(responseStr);

            // Then the system should return an error message
            assertEquals("ERROR", response.get("result").asText());
            assertTrue(response.get("data").get("message").asText().contains("already exists"));
        }   catch (Exception e) {
            fail("Test failed due to exception: " + e.getMessage());
        }
    }

    /**
     * Given a world "world-1" exists
     */
    @Test
    @Order(4)
    void shouldAllowOverwriteWhenForceFlagProvided() {
        try (Socket socket = new Socket("localhost", PORT);
             PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
             BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()))) {

            // When I send the SAVE command with --force
            String saveWithForce = "{ \"robot\": \"A\", \"command\": \"save\", \"arguments\": [\"world-1\", \"--force\"] }";
            out.println(saveWithForce);
            String responseStr = in.readLine();
            System.out.println("Save Response: " + responseStr);
            JsonNode response = objectMapper.readTree(responseStr);

            // Then the world should be overwritten successfully
            assertEquals("OK", response.get("result").asText());
            assertTrue(response.get("data").get("message").asText().contains("saved"));
        } catch (Exception e) {
            fail("Test failed due to exception: " + e.getMessage());
        }
    }

    /**
     * Given a running server
     */
    @Test
    @Order(5)
    void shouldSaveMultipleUniqueWorlds() {
        String[] worldNames = { "alpha", "beta", "gamma" };

        try (Socket socket = new Socket("localhost", PORT);
             PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
             BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()))) {

            clearDatabase();

            for (String worldName : worldNames) {
                // When I send the SAVE command with the unique world name
                String saveCommand = "{ \"robot\": \"A\", \"command\": \"save\", \"arguments\": [\"" + worldName + "\"] }";
                out.println(saveCommand);
                String responseStr = in.readLine();
                System.out.println("Save Response for " + worldName + ": " + responseStr);
                JsonNode response = objectMapper.readTree(responseStr);

                // Then the response should confirm successful saving
                assertEquals("OK", response.get("result").asText(), "Save failed for " + worldName);
                assertTrue(response.get("data").get("message").asText().contains("saved"), "Missing success message for " + worldName);
            }
        } catch (Exception e) {
            fail("Exception during test: " + e.getMessage());
        }
    }

    /**
     * Given worlds "alpha" and "beta" were saved
     */
    @Test
    @Order(6)
    void shouldRestoreMultipleNamedWorlds() {
        String[] worldNames = { "alpha", "beta" };

        try (Socket socket = new Socket("localhost", PORT);
             PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
             BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()))) {

            for (String worldName : worldNames) {
                // When I send the RESTORE command for each saved world
                String restoreCommand = "{ \"robot\": \"A\", \"command\": \"restore\", \"arguments\": [\"" + worldName + "\"] }";
                out.println(restoreCommand);
                String responseStr = in.readLine();
                System.out.println("Restore Response for " + worldName + ": " + responseStr);
                JsonNode response = objectMapper.readTree(responseStr);

                // Then the response should confirm successful restoration
                assertEquals("OK", response.get("result").asText(), "Restore failed for " + worldName);
                assertTrue(response.get("data").get("message").asText().contains("restored"), "Missing restore message for " + worldName);
            }
        } catch (Exception e) {
            fail("Exception during test: " + e.getMessage());
        }
    }
}
