package za.co.wethinkcode.robots.acceptanceTest;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import za.co.wethinkcode.robots.ServerExtension;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(ServerExtension.class)
public class RobotTurningTests {
    private static final String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();
    
    @BeforeEach
    public void connectToServer() throws IOException {
        int port = ServerExtension.getServerPort();
        serverClient.connect(DEFAULT_IP, port);
    }

    @AfterEach
    public void disconnectFromServer(){
        serverClient.disconnect();
    }
    
    @Test
    void testTurnCommand90DegreesClockWise() {
        String robotName = "HAL";

        if (!launchRobot(robotName)) {
            fail("Failed to launch robot");
            return;
        }

       assertEquals("NORTH",  directionForRobot(robotName));
       assertEquals("EAST",  turnRobot(robotName, true));
    }

    @Test
    void testTurnCommand180DegreesClockWise() {
        String robotName = "HAL";

        if (!launchRobot(robotName)) {
            fail("Failed to launch robot");
            return;
        }

        assertEquals("NORTH",  directionForRobot(robotName));
        turnRobot(robotName, true);
        assertEquals("SOUTH",  turnRobot(robotName, true));
    }

    @Test
    void testTurnCommand270DegreesClockWise() {
        String robotName = "HAL";

        if (!launchRobot(robotName)) {
            fail("Failed to launch robot");
            return;
        }

        assertEquals("NORTH",  directionForRobot(robotName));
        turnRobot(robotName, true);
        turnRobot(robotName, true);
        assertEquals("WEST",  turnRobot(robotName, true));
    }

    @Test
    void testTurnCommand360DegreesClockWise() {
        String robotName = "HAL";

        if (!launchRobot(robotName)) {
            fail("Failed to launch robot");
            return;
        }

        assertEquals("NORTH",  directionForRobot(robotName));
        turnRobot(robotName, true);
        turnRobot(robotName, true);
        turnRobot(robotName, true);
        assertEquals("NORTH",  turnRobot(robotName, true));
    }

    @Test
    void testTurnCommand90DegreesAntiClockWise() {
        String robotName = "HAL";

        if (!launchRobot(robotName)) {
            fail("Failed to launch robot");
            return;
        }

        assertEquals("NORTH",  directionForRobot(robotName));
        assertEquals("WEST",  turnRobot(robotName, false));
    }

    @Test
    void testTurnCommand180DegreesAntiClockWise() {
        String robotName = "HAL";

        if (!launchRobot(robotName)) {
            fail("Failed to launch robot");
            return;
        }

        assertEquals("NORTH",  directionForRobot(robotName));
        turnRobot(robotName, false);
        assertEquals("SOUTH",  turnRobot(robotName, false));
    }

    @Test
    void testTurnCommand270DegreesAntiClockWise() {
        String robotName = "HAL";

        if (!launchRobot(robotName)) {
            fail("Failed to launch robot");
            return;
        }

        assertEquals("NORTH",  directionForRobot(robotName));
        turnRobot(robotName, false);
        turnRobot(robotName, false);
        assertEquals("EAST",  turnRobot(robotName, false));
    }

    @Test
    void testTurnCommand360DegreesAntiClockWise() {
        String robotName = "HAL";

        if (!launchRobot(robotName)) {
            fail("Failed to launch robot");
            return;
        }

        assertEquals("NORTH",  directionForRobot(robotName));
        turnRobot(robotName, false);
        turnRobot(robotName, false);
        turnRobot(robotName, false);
        assertEquals("NORTH",  turnRobot(robotName, false));
    }


    private String directionForRobot(String robotName) {
        String stateRequest = String.format("{\"robot\": \"%s\", \"command\": \"state\", \"arguments\": []}", robotName);
        JsonNode stateResponse;
        String direction = null;

        try {
            stateResponse = serverClient.sendRequest(stateRequest);
            direction = stateResponse.get("state").get("direction").asText();
        } catch (Exception ignored) {}

        return direction;
    }

    private String turnRobot(String robotName, boolean clockwise) {
        String heading = clockwise ? "right" : "left";
        String turnRequest = String.format("{\"robot\": \"%s\", \"command\": \"turn\", \"arguments\": [\"%s\"]}", robotName, heading);
        String direction = null;

        try {
            serverClient.sendRequest(turnRequest);
            direction = directionForRobot(robotName);
        } catch (Exception ignored) {}

        return direction;
    }

    private boolean launchRobot(String robotName) {
        boolean result = false;
        String launchRequest = String.format(
                "{\"robot\": \"%s\", \"command\": \"launch\", \"arguments\": [\"shooter\",\"5\",\"5\"]}",
                robotName
        );

        try {
            serverClient.sendRequest(launchRequest);
            result = true;
        } catch (Exception ignored) {}

        return result;
    }
}
