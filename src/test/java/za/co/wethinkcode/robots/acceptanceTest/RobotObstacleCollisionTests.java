package za.co.wethinkcode.robots.acceptanceTest;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import za.co.wethinkcode.robots.ServerConfig;
import za.co.wethinkcode.robots.ServerExtension;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * As a player,
 * I want my robot to be blocked by obstacles when moving
 * So that the world physics are realistic
 */
@ExtendWith(ServerExtension.class)
class RobotObstacleCollisionTests {

    private static final String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    void connectToServer() throws IOException {
        int port = ServerExtension.getServerPort();
        serverClient.connect(DEFAULT_IP, port);
    }

    @AfterEach
    void disconnectFromServer() {
        serverClient.disconnect();
    }

    private JsonNode launchRobot(String robotName, String type, String x, String y) {
        String launchRequest = String.format(
                "{\"robot\": \"%s\", \"command\": \"launch\", \"arguments\": [\"%s\", \"%s\", \"%s\"]}",
                robotName, type, x, y);
        try {
            JsonNode response = serverClient.sendRequest(launchRequest);
            assertEquals("OK", response.get("result").asText(), "Failed to launch robot " + robotName);
            return response;
        } catch (Exception e) {
            fail("Launch request failed for " + robotName + ": " + e.getMessage());
            return null;
        }
    }

    private JsonNode sendMoveCommand(String robotName, String direction, String steps) {
        String moveRequest = String.format(
                "{\"robot\": \"%s\", \"command\": \"%s\", \"arguments\": [\"%s\"]}",
                robotName, direction, steps);
        try {
            return serverClient.sendRequest(moveRequest);
        } catch (Exception e) {
            fail("Move request failed for " + robotName + ": " + e.getMessage());
            return null;
        }
    }

    private JsonNode sendTurnCommand(String robotName, String direction) {
        String turnRequest = String.format(
                "{\"robot\": \"%s\", \"command\": \"turn\", \"arguments\": [\"%s\"]}",
                robotName, direction);
        try {
            return serverClient.sendRequest(turnRequest);
        } catch (Exception e) {
            fail("Turn request failed for " + robotName + ": " + e.getMessage());
            return null;
        }
    }

    private JsonNode setupAndMoveRobot(String robotName, String type, String x, String y, String direction, String steps) {
        launchRobot(robotName, type, x, y);
        return sendMoveCommand(robotName, direction, steps);
    }

    private void assertObstructedResponse(JsonNode response, String expectedMessage, String testDescription) {
        assertEquals("ERROR", response.get("result").asText(), testDescription + ": Expected ERROR result");
        assertTrue(response.get("data").has("message"), testDescription + ": Response should include error message");
        assertTrue(response.get("data").get("message").asText().contains(expectedMessage),
                testDescription + ": Expected '" + expectedMessage + "' message, got: " + response.get("data").get("message").asText());
    }

    private void assertPosition(JsonNode response, int expectedX, int expectedY, String testDescription) {
        JsonNode state = response.get("state");
        assertNotNull(state, testDescription + ": State should be present");
        assertEquals(expectedX, state.get("position").get(0).asInt(), testDescription + ": Robot x position should be " + expectedX);
        assertEquals(expectedY, state.get("position").get(1).asInt(), testDescription + ": Robot y position should be " + expectedY);
    }

    private void assertSuccessfulMove(JsonNode response, String testDescription) {
        assertEquals("OK", response.get("result").asText(), testDescription + ": Expected OK for valid move");
    }

    @Test
    @ServerConfig(arguments = {"-s", "5", "-o", "0,1"})
    void testForwardMovementIntoObstacle() {
        JsonNode response = setupAndMoveRobot("Alpha", "tank", "0", "0", "forward", "1");
        assertObstructedResponse(response, "HitObstacle", "Forward movement into obstacle");
        assertPosition(response, 0, 0, "Forward movement into obstacle");
    }

    @Test
    @ServerConfig(arguments = {"-s", "5", "-o", "0,-1"})
    void testBackwardMovementIntoObstacle() {
        JsonNode response = setupAndMoveRobot("Alpha", "tank", "0", "0", "back", "1");
        assertObstructedResponse(response, "HitObstacle", "Backward movement into obstacle");
        assertPosition(response, 0, 0, "Backward movement into obstacle");
    }

    @Test
    @ServerConfig(arguments = {"-s", "5", "-o", "0,3"})
    void testPartialMovementBeforeObstacle() {
        JsonNode response = setupAndMoveRobot("Alpha", "tank", "0", "0", "forward", "3");
        assertObstructedResponse(response, "HitObstacle", "Partial movement before obstacle");
        assertPosition(response, 0, 2, "Partial movement before obstacle");
    }

    @Test
    @ServerConfig(arguments = {"-s", "5", "-o", "0,1"})
    void testAdjacentToObstacleMovement() {
        JsonNode response = setupAndMoveRobot("Alpha", "tank", "0", "0", "forward", "1");
        assertObstructedResponse(response, "HitObstacle", "Adjacent to obstacle movement");
        assertPosition(response, 0, 0, "Adjacent to obstacle movement");
    }

    @Test
    @ServerConfig(arguments = {"-s", "5", "-o", "0,2"})
    void testMultipleObstaclesInPath() {
        JsonNode response = setupAndMoveRobot("Alpha", "tank", "0", "0", "forward", "5");
        assertObstructedResponse(response, "HitObstacle", "Multiple obstacles in path");
        assertPosition(response, 0, 1, "Multiple obstacles in path");
    }

    @Test
    @ServerConfig(arguments = {"-s", "5", "-o", "-1,1"})
    void testLargeObstacleCollision() {
        JsonNode response = setupAndMoveRobot("Alpha", "tank", "0", "0", "forward", "2");
        assertObstructedResponse(response, "HitObstacle", "Large obstacle collision");
        assertPosition(response, 0, 0, "Large obstacle collision");
    }

    @Test
    @ServerConfig(arguments = {"-s", "5", "-o", "0,0"})
    void testCornerObstacleNavigation() {
        launchRobot("Alpha", "tank", "-1", "-1");
        JsonNode forwardResponse = sendMoveCommand("Alpha", "forward", "2");
        assertObstructedResponse(forwardResponse, "HitObstacle", "Corner obstacle forward movement");
        assertPosition(forwardResponse, -1, -1, "Corner obstacle forward movement");
        JsonNode turnResponse = sendTurnCommand("Alpha", "right");
        assertEquals("OK", turnResponse.get("result").asText(), "Turn command failed");
        JsonNode moveResponse = sendMoveCommand("Alpha", "forward", "1");
        assertSuccessfulMove(moveResponse, "Corner obstacle navigation after turn");
        assertPosition(moveResponse, 0, -1, "Corner obstacle navigation after turn");
    }

    @Test
    @ServerConfig(arguments = {"-s", "5"})
    void testRobotToRobotCollision() {
        launchRobot("Alpha", "tank", "0", "0");
        launchRobot("Beta", "tank", "0", "1");
        JsonNode response = sendMoveCommand("Alpha", "forward", "1");
        assertObstructedResponse(response, "HitRobot", "Robot to robot collision");
        assertPosition(response, 0, 0, "Robot to robot collision");
    }
}