package za.co.wethinkcode.robots.acceptanceTest;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import za.co.wethinkcode.robots.ServerConfig;
import za.co.wethinkcode.robots.ServerExtension;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;
import za.co.wethinkcode.robots.server.Obstacle;
import za.co.wethinkcode.robots.server.db.DatabaseConnection;
import za.co.wethinkcode.robots.server.db.WorldDAOImpl;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(ServerExtension.class)
public class RestoreWorldTests {
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    public void connectToServer() throws IOException {
        int port = ServerExtension.getServerPort();
        serverClient.connect(DEFAULT_IP, port);
    }

    @AfterEach
    public void disconnectFromServer() {
        serverClient.disconnect();
    }

    @Test
    @ServerConfig(arguments = {"-o", "0,1"})
    void testRestore() throws SQLException {
        // Note we're using a random name here to prevent using an existing db name
        // which can corrupt our tests
        String randomWorldName = "TestWorld-" + UUID.randomUUID().toString();
        // Given that I sent a save request
        String request = "{\"robot\":\"\",\"command\":\"save\",\"arguments\":[\"" + randomWorldName + "\"]}";
        JsonNode response = serverClient.sendRequest(request);

        // Then I should get back an okay response
        assertEquals("OK", response.get("result").asText());

        // After saving and sending a restore request
        String restoreRequest = "{\"robot\":\"\",\"command\":\"restore\",\"arguments\":[\"" + randomWorldName + "\"]}";
        JsonNode restoreResponse = serverClient.sendRequest(restoreRequest);

        // I should also get back an okay response
        assertEquals("OK", restoreResponse.get("result").asText());

        // Then the restored obstacle in the saved world should have the same x,y
        Connection conn = DatabaseConnection.getConnection();
        List<Obstacle> obstacles = WorldDAOImpl.obstaclesFromWorld(randomWorldName, conn);
        Obstacle restoredObstacle = obstacles.getFirst();

        assertEquals(1, obstacles.size());
        assertEquals(0, restoredObstacle.getX());
        assertEquals(1, restoredObstacle.getY());
    }
}
