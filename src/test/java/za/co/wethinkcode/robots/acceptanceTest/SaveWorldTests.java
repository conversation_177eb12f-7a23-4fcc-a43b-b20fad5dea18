package za.co.wethinkcode.robots.acceptanceTest;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import za.co.wethinkcode.robots.ServerConfig;
import za.co.wethinkcode.robots.ServerExtension;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;
import za.co.wethinkcode.robots.server.db.DatabaseConnection;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;

import static org.junit.jupiter.api.Assertions.*;

/**
 * As a player,
 * I want to save the current world state
 * So that I can preserve my progress and continue later
 */
@ExtendWith(ServerExtension.class)
public class SaveWorldTests {
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    public void connectToServer() throws IOException {
        int port = ServerExtension.getServerPort();
        serverClient.connect(DEFAULT_IP, port);
    }

    @AfterEach
    public void disconnectFromServer() {
        serverClient.disconnect();
    }

    @Test
    @ServerConfig(arguments = {"-s", "10"})
    void testSaveWorldCommand() throws Exception {
        // Given that I am connected to a running Robot Worlds server
        assertTrue(serverClient.isConnected());

        // When I send a save command
        String request = "{\"robot\":\"testRobot\",\"command\":\"save\",\"arguments\":[\"testWorld\"]}";
        JsonNode response = serverClient.sendRequest(request);

        // Then I should get a successful response
        assertEquals("OK", response.get("result").asText());
        assertEquals("World 'testWorld' saved successfully", response.get("data").get("message").asText());

        // And the world should be saved in the database
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement("SELECT * FROM worlds WHERE name = ?")) {
            pstmt.setString(1, "testWorld");
            ResultSet rs = pstmt.executeQuery();
            assertTrue(rs.next(), "World should be saved");
            assertEquals("testWorld", rs.getString("name"));
            assertEquals(11, rs.getInt("width"));
            assertEquals(11, rs.getInt("height"));
        }
    }

    @Test
    @ServerConfig(arguments = {"-s", "10"})
    void testSaveWorldInvalidArguments() throws Exception {
        // Given that I am connected to a running Robot Worlds server
        assertTrue(serverClient.isConnected());

        // When I send a save command with no arguments
        String request = "{\"robot\":\"testRobot\",\"command\":\"save\",\"arguments\":[]}";
        JsonNode response = serverClient.sendRequest(request);

        // Then I should get an error response
        assertEquals("ERROR", response.get("result").asText());
        assertEquals("Invalid arguments: Usage: save <worldName>", response.get("data").get("message").asText());
    }

    @Test
    @ServerConfig(arguments = {"-s", "10"})
    void testSaveWorldEmptyName() throws Exception {
        // Given that I am connected to a running Robot Worlds server
        assertTrue(serverClient.isConnected());

        // When I send a save command with empty name
        String request = "{\"robot\":\"testRobot\",\"command\":\"save\",\"arguments\":[\" \"]}";
        JsonNode response = serverClient.sendRequest(request);

        // Then I should get an error response
        assertEquals("ERROR", response.get("result").asText());
        assertEquals("Invalid arguments: Usage: save <worldName>", response.get("data").get("message").asText());
    }
}
