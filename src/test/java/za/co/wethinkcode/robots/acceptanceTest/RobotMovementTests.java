package za.co.wethinkcode.robots.acceptanceTest;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import za.co.wethinkcode.robots.ServerConfig;
import za.co.wethinkcode.robots.ServerExtension;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(ServerExtension.class)
public class RobotMovementTests {
    private final static String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();

    @BeforeEach
    public void connectToServer() throws IOException {
        int port = ServerExtension.getServerPort();
        serverClient.connect(DEFAULT_IP, port);
    }

    @AfterEach
    public void disconnectFromServer(){
        serverClient.disconnect();
    }

    @Test
    @ServerConfig(arguments = {"-s", "1"})
    public void testMoveForward() {
        // Implement test for moving forward
        // I want to command my robot to move forward
        // So that I can explore the world and not be a sitting duck in battle
        //Given the world is 1x1 with no obstacles
        //And I have launched a robot into the world
        //When I command the robot to move forward for 5 steps
        //Then I get an "OK" response with message "At the NORTH edge "
        //And the robot's position remains [0,0]

        // Given that I am connected to a running Robot Worlds server
        assertTrue(serverClient.isConnected());

        // And I have launched a robot into the world
        String launchRequest = "{" +
                "  \"robot\": \"TestRobot\"," +
                "  \"command\": \"launch\"," +
                "  \"arguments\": [\"tank\",\"5\",\"5\"]" +
                "}";
        JsonNode launchResponse = serverClient.sendRequest(launchRequest);

        // Verify launch was successful
        assertEquals("OK", launchResponse.get("result").asText());

        // When I command the robot to move forward for 5 steps
        String moveRequest = "{" +
                "  \"robot\": \"TestRobot\"," +
                "  \"command\": \"forward\"," +
                "  \"arguments\": [\"5\"]" +
                "}";
        JsonNode moveResponse = serverClient.sendRequest(moveRequest);

        // Then I get an "OK" response with message "At the NORTH edge"
        assertEquals("OK", moveResponse.get("result").asText());
        assertTrue(moveResponse.get("data").get("message").asText().contains("At the NORTH edge"));

        // And the robot's position remains [0,0]
        JsonNode state = moveResponse.get("state");
        JsonNode position = state.get("position");
        assertEquals(0, position.get(0).asInt());
        assertEquals(0, position.get(1).asInt());
    }
//
//    @Test
//    public void testMoveBackward() {
//        // Implement test for moving backward
//        // I want to command my robot to move back
//        // So that I can explore the world and not be a sitting duck in battle
//        //Given the world is 1x1 with no obstacles
//        //And I have launched a robot into the world
//        //When I command the robot to move back for 5 steps
//        //Then I get an "OK" response with message "At the SOUTH edge "
//        //And the robot's position remains [0,0]
//
//        // Given that I am connected to a running Robot Worlds server
//        assertTrue(serverClient.isConnected());
//
//        // And I have launched a robot into the world
//        String launchRequest = "{" +
//                "  \"robot\": \"Test2Robot\"," +
//                "  \"command\": \"launch\"," +
//                "  \"arguments\": [\"tank\",\"5\",\"5\"]" +
//                "}";
//        JsonNode launchResponse = serverClient.sendRequest(launchRequest);
//
//        // Verify launch was successful
//        assertEquals("OK", launchResponse.get("result").asText());
//
//        // When I command the robot to move back for 5 steps
//        String moveRequest = "{" +
//                "  \"robot\": \"Test2Robot\"," +
//                "  \"command\": \"back\"," +
//                "  \"arguments\": [\"5\"]" +
//                "}";
//        JsonNode moveResponse = serverClient.sendRequest(moveRequest);
//
//        // Then I get an "OK" response with message "At the SOUTH edge"
//        assertEquals("OK", moveResponse.get("result").asText());
//        assertTrue(moveResponse.get("data").get("message").asText().contains("At the SOUTH edge"));
//
//        // And the robot's position remains [0,0]
//        JsonNode state = moveResponse.get("state");
//        JsonNode position = state.get("position");
//        assertEquals(0, position.get(0).asInt());
//        assertEquals(0, position.get(1).asInt());
//    }
}
