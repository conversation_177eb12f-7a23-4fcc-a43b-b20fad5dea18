package za.co.wethinkcode.robots.acceptanceTest;

import com.fasterxml.jackson.databind.JsonNode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import za.co.wethinkcode.robots.ServerConfig;
import za.co.wethinkcode.robots.ServerExtension;
import za.co.wethinkcode.robots.client.RobotWorldClient;
import za.co.wethinkcode.robots.client.RobotWorldJsonClient;
import za.co.wethinkcode.robots.server.ConfigLoader;
import za.co.wethinkcode.robots.server.World;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

/**
 * As a player,
 * I want to look around and see other robots in my view
 * So that I can navigate strategically or engage them in battle
 */
@ExtendWith(ServerExtension.class)
class RobotLookTests {

    private static final String DEFAULT_IP = "localhost";
    private final RobotWorldClient serverClient = new RobotWorldJsonClient();


    @BeforeEach
    void connect() throws IOException {
        int port = ServerExtension.getServerPort();
        serverClient.connect(DEFAULT_IP, port);
    }

    @AfterEach
    void disconnect() {
        serverClient.disconnect();
    }

    @Test
    void robotShouldBeAbleToSee() {
        // Given a robot is launched into the world
        String launchFBI = "{" +
                "  \"robot\": \"FBI\"," +
                "  \"command\": \"launch\"," +
                "  \"arguments\": [\"shooter\",\"5\",\"5\"]" +
                "}";
        JsonNode responseFBI = serverClient.sendRequest(launchFBI);
        assertEquals("OK", responseFBI.get("result").asText());

        // When "FBI" performs the look command
        String lookFBI = "{" +
                "\"robot\": \"FBI\"," +
                "\"command\": \"look\"," +
                "\"arguments\": []" +
                "}";


        JsonNode lookResponse = serverClient.sendRequest(lookFBI);
        assertEquals("OK", lookResponse.get("result").asText());
        int visibility = lookResponse.get("data").get("visibility").asInt();
        assertNotEquals(0, visibility, "The robot should have a positive visibility or it won't be able to see ");

    }

    @Test
    @ServerConfig(arguments = {"-s", "2", "-o", "0,1"})
    void detectNearbyRobotsAndObstacle() {
        // Given a 3x3 world (from -s 2) and 8 robots launched
        String[] robotNames = {"Alpha", "Beta", "Gamma", "Delta", "Echo", "Foxtrot", "Ghost", "Hawk"};

        for (int i = 0; i < robotNames.length; i++) {
            String launchRequest = "{" +
                    "\"robot\": \"" + robotNames[i] + "\"," +
                    "\"command\": \"launch\"," +
                    "\"arguments\": [\"tank\", \"" + 5 + "\", \"" + 5 + "\"]" +
                    "}";
            JsonNode response = serverClient.sendRequest(launchRequest);
            assertEquals("OK", response.get("result").asText(), "Robot " + robotNames[i] + " failed to launch.");
        }

        // When Alpha sends the look command
        String lookCommand = "{" +
                "\"robot\": \"Alpha\"," +
                "\"command\": \"look\"," +
                "\"arguments\": []" +
                "}";
        JsonNode lookResponse = serverClient.sendRequest(lookCommand);
        assertEquals("OK", lookResponse.get("result").asText(), "Look command failed.");

        // Then the response includes: → 1 OBSTACLE one step away → 3 ROBOTs one step away
        JsonNode objects = lookResponse.get("data").get("objects");
        assertNotNull(objects, "Objects array missing from look response.");

        int obstacleCount = 0;
        int robotCount = 0;

        for (JsonNode obj : objects) {
            String type = obj.get("type").asText();
            int distance = obj.get("distance").asInt();

            if ("OBSTACLE".equalsIgnoreCase(type) && distance == 1) {
                obstacleCount++;
            } else if ("ROBOT".equalsIgnoreCase(type) && distance == 1) {
                robotCount++;
            }
        }

        assertEquals(1, obstacleCount, "Expected 1 OBSTACLE one step away.");
        assertEquals(3, robotCount, "Expected 3 ROBOTs one step away.");
    }

    @Test
    @ServerConfig(arguments = {"-s", "2", "-o", "0,1"})
    void detectObstacleInLineOfSight() {
        // Given a 2x2 world with an obstacle at [0,1]
        // And the robot is at [0,0] facing North
        String launch = "{" +
                "\"robot\": \"Alpha\"," +
                "\"command\": \"launch\"," +
                "\"arguments\": [\"tank\", \"2\", \"2\"]" +
                "}";
        JsonNode launchResponse = serverClient.sendRequest(launch);
        assertEquals("OK", launchResponse.get("result").asText());

        // When I issue the look command
        String look = "{" +
                "\"robot\": \"Alpha\"," +
                "\"command\": \"look\"," +
                "\"arguments\": []" +
                "}";
        JsonNode lookResponse = serverClient.sendRequest(look);
        assertEquals("OK", lookResponse.get("result").asText());

        // Then the response includes: → OBSTACLE at distance 1
        JsonNode objects = lookResponse.get("data").get("objects");
        assertNotNull(objects);
        boolean obstacleFound = false;

        for (JsonNode obj : objects) {
            if (obj.get("type").asText().equalsIgnoreCase("OBSTACLE") &&
                    obj.get("distance").asInt() == 1) {
                obstacleFound = true;
                break;
            }
        }

        assertTrue(obstacleFound, "Expected an OBSTACLE at distance 1 in the robot's facing direction.");
    }

}
