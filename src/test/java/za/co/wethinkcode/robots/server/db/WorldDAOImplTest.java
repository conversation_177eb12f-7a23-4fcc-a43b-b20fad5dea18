package za.co.wethinkcode.robots.server.db;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.server.World;
import za.co.wethinkcode.robots.server.Obstacle;
import za.co.wethinkcode.robots.server.ObstacleType;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class WorldDAOImplTest {
    private WorldDAO worldDAO;
    private World mockWorld;
    private final boolean alwaysOverwrite = true;

    @BeforeEach
    void setUp() throws SQLException {
        worldDAO = new WorldDAOImpl();
        mockWorld = mock(World.class);
        DatabaseConnection.initializeDatabase(); // Ensure tables exist
        try (Connection conn = DatabaseConnection.getConnection()) {
            conn.prepareStatement("DELETE FROM obstacles").executeUpdate();
            conn.prepareStatement("DELETE FROM worlds").executeUpdate();
        }
    }

    @Test
    void testSaveWorld() throws SQLException {
        // Arrange
        when(mockWorld.getWidth()).thenReturn(10);
        when(mockWorld.getHeight()).thenReturn(20);
        Obstacle obstacle1 = new Obstacle(ObstacleType.MOUNTAIN, 1, 2, 3, 4);
        Obstacle obstacle2 = new Obstacle(ObstacleType.PIT, -1, -2, 2, 2);
        when(mockWorld.getObstacles()).thenReturn(Arrays.asList(obstacle1, obstacle2));

        // Act
        worldDAO.saveWorld("testWorld", mockWorld, alwaysOverwrite);

        // Assert: Verify world data
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement("SELECT * FROM worlds WHERE name = ?")) {
            pstmt.setString(1, "testWorld");
            ResultSet rs = pstmt.executeQuery();
            assertTrue(rs.next(), "World should be saved");
            assertEquals("testWorld", rs.getString("name"));
            assertEquals(10, rs.getInt("width"));
            assertEquals(20, rs.getInt("height"));
            assertFalse(rs.next(), "Only one world should be saved");
        }

        // Assert: Verify obstacles
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement("SELECT * FROM obstacles WHERE world_name = ?")) {
            pstmt.setString(1, "testWorld");
            ResultSet rs = pstmt.executeQuery();
            int obstacleCount = 0;
            while (rs.next()) {
                obstacleCount++;
                String type = rs.getString("type");
                int x = rs.getInt("x");
                int y = rs.getInt("y");
                int width = rs.getInt("width");
                int height = rs.getInt("height");
                if (type.equals("MOUNTAIN")) {
                    assertEquals(1, x);
                    assertEquals(2, y);

                } else if (type.equals("PIT")) {
                    assertEquals(-1, x);
                    assertEquals(-2, y);

                }
            }
            assertEquals(2, obstacleCount, "Two obstacles should be saved");
        }
    }

    @Test
    void testSaveWorldEmptyObstacles() throws SQLException {
        // Arrange
        when(mockWorld.getWidth()).thenReturn(5);
        when(mockWorld.getHeight()).thenReturn(5);
        when(mockWorld.getObstacles()).thenReturn(Arrays.asList());

        // Act
        worldDAO.saveWorld("emptyWorld", mockWorld, alwaysOverwrite);

        // Assert: Verify world data
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement("SELECT * FROM worlds WHERE name = ?")) {
            pstmt.setString(1, "emptyWorld");
            ResultSet rs = pstmt.executeQuery();
            assertTrue(rs.next(), "World should be saved");
            assertEquals("emptyWorld", rs.getString("name"));
            assertEquals(5, rs.getInt("width"));
            assertEquals(5, rs.getInt("height"));
        }

        // Assert: Verify no obstacles
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement("SELECT COUNT(*) FROM obstacles WHERE world_name = ?")) {
            pstmt.setString(1, "emptyWorld");
            ResultSet rs = pstmt.executeQuery();
            assertTrue(rs.next());
            assertEquals(0, rs.getInt(1), "No obstacles should be saved");
        }
    }

    @Test
    void testSaveWorldReplacesExisting() throws SQLException {
        // Arrange
        when(mockWorld.getWidth()).thenReturn(10);
        when(mockWorld.getHeight()).thenReturn(20);
        when(mockWorld.getObstacles()).thenReturn(Arrays.asList(new Obstacle(ObstacleType.MOUNTAIN, 1, 2, 3, 4)));

        // Save first version
        worldDAO.saveWorld("replaceWorld", mockWorld, alwaysOverwrite);

        // Update mock to simulate different data
        when(mockWorld.getWidth()).thenReturn(15);
        when(mockWorld.getHeight()).thenReturn(25);
        when(mockWorld.getObstacles()).thenReturn(Arrays.asList(new Obstacle(ObstacleType.PIT, 0, 0, 2, 2)));

        // Act: Save again
        worldDAO.saveWorld("replaceWorld", mockWorld, alwaysOverwrite);

        // Assert: Verify updated world data
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement("SELECT * FROM worlds WHERE name = ?")) {
            pstmt.setString(1, "replaceWorld");
            ResultSet rs = pstmt.executeQuery();
            assertTrue(rs.next(), "World should be updated");
            assertEquals(15, rs.getInt("width"));
            assertEquals(25, rs.getInt("height"));
        }

        // Assert: Verify updated obstacles (old ones should be gone)
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement("SELECT * FROM obstacles WHERE world_name = ?")) {
            pstmt.setString(1, "replaceWorld");
            ResultSet rs = pstmt.executeQuery();
            assertTrue(rs.next(), "One obstacle should be saved");
            assertEquals("PIT", rs.getString("type"));
            assertEquals(0, rs.getInt("x"));
            assertEquals(0, rs.getInt("y"));
            assertEquals(2, rs.getInt("width"));
            assertEquals(2, rs.getInt("height"));
            assertFalse(rs.next(), "Only one obstacle should be saved");
        }
    }

    @Test
    void testRestoringWorld() throws SQLException {
        World world = new World(3, 3);
        Obstacle obstacle = new Obstacle(ObstacleType.MOUNTAIN, 0, 0, 1, 1);

        assertTrue(world.addObstacle(obstacle));
        assertDoesNotThrow(() -> {
            worldDAO.saveWorld("test_world", world, alwaysOverwrite);
            World restoredWorld = worldDAO.restoreWorld("test_world");

            assertEquals(restoredWorld.getHeight(), world.getHeight());
            assertEquals(restoredWorld.getWidth(), world.getWidth());

            assertEquals(1, restoredWorld.getObstacles().size());
            assertEquals(1, world.getObstacles().size());

            Obstacle restoredObstacle = restoredWorld.getObstacles().getFirst();

            assertEquals(restoredObstacle, obstacle);
        });
    }
}