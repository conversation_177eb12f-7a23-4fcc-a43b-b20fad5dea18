package za.co.wethinkcode.robots.server;

import static org.junit.jupiter.api.Assertions.*;
import java.io.*;

import org.junit.jupiter.api.*;

import java.net.Socket;
import java.net.ServerSocket;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
// we want the shutdown test to run last
public class ServerTest {
    private static final int TEST_PORT = 12345;
    private static Thread serverThread;

    @BeforeAll
    public static void setUpServer() throws Exception {
        serverThread = new Thread(() -> {
            try {
                za.co.wethinkcode.robots.server.Server.main(new String[]{String.valueOf(TEST_PORT), "n"});
            } catch (Exception e) {
                System.err.println("Server startup error: " + e.getMessage());
            }
        });
        serverThread.start();


        // Wait for server to be properly initialized
        int attempts = 0;
        while (!Server.isServerInitialized() && attempts < 10) {
            attempts++;
        }
    }

    @Test
    @Order(1)
    public void testServerShutdown() throws Exception {
        // Only test shutdown if server is properly initialized
        if (Server.isServerInitialized()) {
            Server.shutdown();

            Thread.sleep(1000);

            assertThrows(IOException.class, () -> {
                try (Socket ignored = new Socket("localhost", TEST_PORT)) {
                    // If we connect, test fails
                }
            });
        } else {
            // If server isn't initialized, just verify shutdown doesn't throw NPE
            assertDoesNotThrow(Server::shutdown);
        }
    }

    @AfterAll
    public static void tearDown() throws Exception {
        if (serverThread != null && serverThread.isAlive()) {
            Server.shutdown();
            serverThread.interrupt();
            serverThread.join(1000);
        }
    }
}
