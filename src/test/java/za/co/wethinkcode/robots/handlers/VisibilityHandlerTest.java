package za.co.wethinkcode.robots.handlers;

import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import za.co.wethinkcode.robots.Robot;
import za.co.wethinkcode.robots.client.Direction;
import za.co.wethinkcode.robots.server.Obstacle;
import za.co.wethinkcode.robots.server.ObstacleType;
import za.co.wethinkcode.robots.server.Response;
import za.co.wethinkcode.robots.server.World;

import static org.junit.jupiter.api.Assertions.*;

public class VisibilityHandlerTest {
    private World world;
    private Robot robot;

    @BeforeEach
    public void setUp() {
        world = new World(10, 10);
        robot = new Robot("Robot", "tank", 0, 0);
        world.addRobot(robot);
        robot.setPosition(0, 0);
    }

    @Test
    public void testVisibility() {
        VisibilityHandler visibilityHandler = new VisibilityHandler(world);
        Response response = visibilityHandler.lookAround(robot);
        assertNotNull(response.object.getJSONObject("data").get("objects"));
        assertTrue(response.object.getJSONObject("data").getJSONArray("objects").length() >= 0);
    }

    @Test
    public void testVisibilityWithObstacle() {
        Obstacle obstacle = new Obstacle(ObstacleType.MOUNTAIN, 1, 0, 1, 1);
        world.addObstacle(obstacle);
        VisibilityHandler visibilityHandler = new VisibilityHandler(world);
        robot.setPosition(3, 0);
        world.displayWorld();
        Response response = visibilityHandler.lookAround(robot);

        JSONArray objects = response.object.getJSONObject("data").getJSONArray("objects");
        boolean foundObstacle = false;
        for (int i = 0; i < objects.length(); i++) {
            JSONObject jsonObject = objects.getJSONObject(i);
            if ("OBSTACLE".equals(jsonObject.getString("type")) &&
                    Direction.CardinalDirection.WEST.equals(jsonObject.get("direction")) &&
                    2 == jsonObject.getInt("distance")) {
                foundObstacle = true;
                break;
            }
        }
        assertTrue(foundObstacle, "Expected to find an OBSTACLE at distance 2 in WEST direction");
        assertFalse(objects.isEmpty());
    }

    @Test
    public void testVisibilityWithRobot() {
        Robot otherRobot = new Robot("OtherRobot", "tank", 1, 0);
        world.addRobot(otherRobot);
        otherRobot.setPosition(1, 0);
        VisibilityHandler visibilityHandler = new VisibilityHandler(world);
        Response response = visibilityHandler.lookAround(robot);

        JSONArray objects = response.object.getJSONObject("data").getJSONArray("objects");
        boolean foundRobot = false;
        for (int i = 0; i < objects.length(); i++) {
            JSONObject jsonObject = objects.getJSONObject(i);
            if ("ROBOT".equals(jsonObject.getString("type")) &&
                    Direction.CardinalDirection.EAST.equals(jsonObject.get("direction")) &&
                    1 == jsonObject.getInt("distance")) {
                foundRobot = true;
                break;
            }
        }
        assertTrue(foundRobot, "Expected to find a ROBOT at distance 1 in EAST direction");
        assertFalse(objects.isEmpty());
    }
}