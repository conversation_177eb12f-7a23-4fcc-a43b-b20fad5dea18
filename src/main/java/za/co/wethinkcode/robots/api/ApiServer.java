package za.co.wethinkcode.robots.api;

import io.javalin.Javalin;
import io.javalin.http.Context;
import za.co.wethinkcode.robots.service.GameService;

/**
 * Javalin-based REST API server for Robot World (Section 3 requirement).
 *
 * This server provides HTTP endpoints for interacting with the Robot World
 * while keeping the domain layer completely independent of HTTP concerns.
 *
 * Dependencies required in pom.xml:
 * - io.javalin:javalin
 * - com.fasterxml.jackson.core:jackson-databind
 * - org.slf4j:slf4j-simple (for logging)
 *
 * Endpoints to implement:
 * - GET /world -> return current world state as JSON
 * - GET /world/{name} -> restore world from database and return as JSON
 * - POST /robot/{name} -> execute robot command (launch initially, extend later)
 *
 * Architecture:
 * - Uses Javalin for HTTP handling
 * - Delegates business logic to GameService
 * - Returns JSON responses using existing Response format
 * - Maintains separation from domain layer
 */
public class ApiServer {

    private final GameService gameService;
    private final int port;
    private Javalin app;

    public ApiServer(GameService gameService, int port) {
        this.gameService = gameService;
        this.port = port;
    }

    /**
     * TODO: Implement server startup and route configuration
     */
    public void start() {
        // TODO: Create Javalin app
        // TODO: Configure JSON serialization
        // TODO: Set up routes
        // TODO: Start server
        System.out.println("TODO: Start Javalin server on port " + port);
    }

    /**
     * TODO: Implement server shutdown
     */
    public void stop() {
        // TODO: Stop Javalin server gracefully
        System.out.println("TODO: Stop Javalin server");
    }

    /**
     * TODO: Implement GET /world endpoint
     */
    private void handleGetCurrentWorld(Context ctx) {
        // TODO: Get current world state from gameService
        // TODO: Convert to JSON and return
    }

    /**
     * TODO: Implement GET /world/{name} endpoint
     */
    private void handleGetWorldByName(Context ctx) {
        // TODO: Extract world name from path parameter
        // TODO: Restore world from database via gameService
        // TODO: Return world state as JSON or 404 if not found
    }

    /**
     * TODO: Implement POST /robot/{name} endpoint
     */
    private void handleRobotCommand(Context ctx) {
        // TODO: Extract robot name from path parameter
        // TODO: Parse JSON request body to Command object
        // TODO: Execute command via gameService
        // TODO: Return response as JSON
    }
}
