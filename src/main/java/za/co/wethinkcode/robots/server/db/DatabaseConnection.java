package za.co.wethinkcode.robots.server.db;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

public class DatabaseConnection {
    private static final String DB_URL = "**************************";

    public static Connection getConnection() throws SQLException {
        return DriverManager.getConnection(DB_URL);
    }

    public static void initializeDatabase() throws SQLException {
        try (Connection conn = getConnection(); Statement stmt = conn.createStatement()) {
            // Enable foreign key support
            stmt.executeUpdate("PRAGMA foreign_keys = ON");

            // Create worlds table
            stmt.executeUpdate(
                    "CREATE TABLE IF NOT EXISTS worlds (" +
                            "name TEXT PRIMARY KEY, " +
                            "width INTEGER NOT NULL, " +
                            "height INTEGER NOT NULL)"
            );

            // Create obstacles table
            stmt.executeUpdate(
                    "CREATE TABLE IF NOT EXISTS obstacles (" +
                            "world_name TEXT, " +
                            "type TEXT NOT NULL CHECK (type IN ('MOUNTAIN', 'PIT', 'MINE')), " +
                            "x INTEGER NOT NULL, " +
                            "y INTEGER NOT NULL, " +
                            "width INTEGER NOT NULL, " +
                            "height INTEGER NOT NULL, " +
                            "PRIMARY KEY (world_name, type, x, y), " +
                            "FOREIGN KEY (world_name) REFERENCES worlds(name) ON DELETE CASCADE)"
            );
        } catch (SQLException e) {
            throw new SQLException("Failed to initialize database: " + e.getMessage(), e);
        }
    }
}