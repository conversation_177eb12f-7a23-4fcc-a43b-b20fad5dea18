package za.co.wethinkcode.robots.server;
import za.co.wethinkcode.flow.Recorder;
import za.co.wethinkcode.robots.handlers.ClientHandler;

import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.Scanner;

/**
 * Main server class that accepts client connections and provides an admin console for server control.
 * Supports real-time robot monitoring, world state inspection, and graceful shutdown.
 */
public class Server {
    private static volatile boolean isRunning = true;
    private static ServerSocket serverSocket;

public static void main(String[] args) {
    int portNumber = 5000;
    int worldSize = 1;
    String obstacleArg;
    ObstacleType obstacleType = ObstacleType.MOUNTAIN;
    int obstacleSize = 1;

    // Typically this shouldn't be needed, but if main is called
    // then shutdown is called then we need to reset this to running
    isRunning = true;

    System.out.println("Arguments " + args);
    // Parse arguments manually
    ArgumentParseResult parsedArgs = parseArguments(args, portNumber, worldSize, null);
    portNumber = parsedArgs.portNumber;
    worldSize = parsedArgs.worldSize;
    obstacleArg = parsedArgs.obstacleArg;

    World world = createWorld(worldSize);

    if (obstacleArg != null && !obstacleArg.equalsIgnoreCase("none")) {
        processObstacleArgument(obstacleArg, world, obstacleType, obstacleSize);
    }

    startServer(portNumber, world);
}

private static ArgumentParseResult parseArguments(String[] args, int portNumber, int worldSize, String obstacleArg) {
    for (int i = 0; i < args.length; i++) {
        switch (args[i]) {
            case "-p":
                if (i + 1 < args.length) portNumber = Integer.parseInt(args[++i]);
                break;
            case "-s":
                if (i + 1 < args.length) worldSize = Integer.parseInt(args[++i]);
                break;
            case "-o":
                if (i + 1 < args.length) obstacleArg = args[++i];
                break;
            default:
                System.out.println("Unknown argument: " + args[i]);
        }
    }
    return new ArgumentParseResult(portNumber, worldSize, obstacleArg);
}

private static World createWorld(int worldSize) {
    World world = new World(worldSize);

    // Always load configuration properties to ensure visibility and other settings are set
    ConfigLoader configLoader = new ConfigLoader();
    configLoader.applyConfigToWorld(world, "config.properties");

    return world;
}

private static void startServer(int portNumber, World world) {
    try {
        serverSocket = new ServerSocket(portNumber);
        System.out.println("Server started on port " + portNumber + ". Waiting for clients...");

        // Try to initialize flow tracking, but continue if it fails (e.g., in Docker)
        try {
            new Recorder().logRun();
        } catch (Exception e) {
            System.out.println("Flow tracking disabled: " + e.getMessage());
        }

        // launch admin console thread
        startAdminConsole(world);
        world.displayWorld();

        while (isRunning) {
            Socket clientSocket = serverSocket.accept();
            System.out.println("New client connected: " + clientSocket.getRemoteSocketAddress());
            new Thread(new ClientHandler(clientSocket, world)).start(); // start new thread to handle multiple clients
        }

    } catch (IOException e) {
        if (!isRunning) {
            System.out.println("Sever shutdown.");
        } else {
            System.out.println("Got an error: " + e);
        }
    }
}

private static class ArgumentParseResult {
    int portNumber;
    int worldSize;
    String obstacleArg;

    ArgumentParseResult(int portNumber, int worldSize, String obstacleArg) {
        this.portNumber = portNumber;
        this.worldSize = worldSize;
        this.obstacleArg = obstacleArg;
    }
}

private static void processObstacleArgument(String obstacleArg, World world, ObstacleType obstacleType, int obstacleSize) {
    String[] coords = obstacleArg.split(",");
    if (coords.length == 2) {
        try {
            int x = Integer.parseInt(coords[0]);
            int y = Integer.parseInt(coords[1]);
            world.addObstacle(new Obstacle(obstacleType, x, y, obstacleSize, obstacleSize), true);
        } catch (NumberFormatException e) {
            System.out.println("Invalid obstacle format. Use: -o x,y (e.g., -o 4,5)");
        }
    } else {
        System.out.println("Invalid obstacle argument. Use: -o x,y");
    }
}

private static void startAdminConsole(World world) {
    new Thread(() -> {
        Scanner scanner = new Scanner(System.in);
        while (isRunning) {
            try {
                System.out.println("Valid Commands: 'quit', 'robots', 'dump', 'display'");
                System.out.print("[Admin]: ");
                String input = scanner.nextLine().trim().toLowerCase();
                switch (input) {
                    case "quit":
                        System.out.println("Shutting down server...");
                        shutdown();
                        break;
                    case "robots":
                         System.out.println(world.getAllRobotsInfo());
                        break;
                    case "dump":
                        System.out.println(world.getFullWorldState());
                        break;
                    case "display":
                        world.displayWorld();
                        break;
                    default:
                        System.out.println("Unknown admin command.");
                }
            } catch (java.util.NoSuchElementException e) {
                // No input available (e.g., during automated testing)
                // Sleep briefly to prevent tight loop
                try {
                    Thread.sleep(100);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
    }, "AdminConsole").start();
}


    public static void shutdown() {
        isRunning = false;
        try {
            if (serverSocket != null && !serverSocket.isClosed()) {
                serverSocket.close();
            }
        } catch (IOException e) {
            System.out.println("Got an error when shutting down: " + e);
        }
    }

    public static boolean isServerInitialized() {

        return serverSocket != null && !serverSocket.isClosed();
    }
}
