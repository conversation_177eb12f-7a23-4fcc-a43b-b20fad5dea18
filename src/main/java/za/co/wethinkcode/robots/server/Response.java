package za.co.wethinkcode.robots.server;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * Represents a standardized response object used for communication between the client and server.
 * Encapsulates a JSON structure with result, message, and optional data for consistency.
 */
public class Response {
    public final JSONObject object;

    public static Response responseFromJSONString(String string) {
        try {
            JSONObject jsonObject = new JSONObject(string);

            return new Response(jsonObject.getString("result"), jsonObject);
        } catch (JSONException e) {
            return new Response("Invalid JSON", "");
        }
    }

public static String formatJsonString(String jsonString) {
    StringBuilder sb = new StringBuilder();
    int indentLevel = 0;
    boolean inString = false;
    boolean escapeNext = false;

    for (int i = 0; i < jsonString.length(); i++) {
        char c = jsonString.charAt(i);

        if (escapeNext) {
            sb.append(c);
            escapeNext = false;
            continue;
        }

        if (c == '\\' && inString) {
            sb.append(c);
            escapeNext = true;
            continue;
        }

        if (c == '"' && !escapeNext) {
            inString = !inString;
            sb.append(c);
            continue;
        }

        if (inString) {
            sb.append(c);
            continue;
        }

        appendFormattedChar(sb, c, indentLevel);

        switch (c) {
            case '{':
            case '[':
                indentLevel++;
                break;
            case '}':
            case ']':
                indentLevel--;
                break;
        }
    }

    return sb.toString();
}

private static void appendFormattedChar(StringBuilder sb, char c, int indentLevel) {
    switch (c) {
        case '{':
        case '[':
            sb.append(c).append('\n');
            for (int j = 0; j < indentLevel + 1; j++) sb.append("    ");
            break;
        case '}':
        case ']':
            sb.append('\n');
            for (int j = 0; j < indentLevel - 1; j++) sb.append("    ");
            sb.append(c);
            break;
        case ',':
            sb.append(c).append('\n');
            for (int j = 0; j < indentLevel; j++) sb.append("    ");
            break;
        case ':':
            sb.append(c).append(' ');
            break;
        case ' ':
            break;
        default:
            sb.append(c);
            break;
    }
}

    public Response(String result, String message) {
        this.object = new JSONObject();
        this.object.put("result", result);
        JSONObject data = new JSONObject();

        data.put("message", message);
        this.object.put("data", data);
    }

    public Response(String result, JSONObject data) {
        this.object = data;
    }


    public static Response ok(JSONObject data, String message) {
        Response response = new Response("OK", message != null ? message : "");

        data.put("message", message != null ? message : "");
        response.object.put("data", data);
        return response;
    }

    public String getMessage() {
        StringBuilder stringBuilder = new StringBuilder();
        if (this.object.getJSONObject("data").has("message")) {
            stringBuilder.append(this.object.getJSONObject("data").get("message"));
        }else
        {
            return formatJsonString(object.getJSONObject("data").toString());
        }
        return stringBuilder.toString();
    }

    public String toJSONString() {
        return this.object.toString();
    }

    public boolean isOKResponse() {
        return this.object.getString("result").equalsIgnoreCase("OK");
    }
}
