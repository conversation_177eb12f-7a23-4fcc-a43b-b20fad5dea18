package za.co.wethinkcode.robots.server.db;

import za.co.wethinkcode.robots.server.Obstacle;
import za.co.wethinkcode.robots.server.World;

import java.sql.SQLException;
import java.util.List;

public interface WorldDAO {
    void saveWorld(String worldName, World world, boolean forceOverwrite) throws SQLException;
    World restoreWorld(String worldName) throws SQLException;
    boolean worldExists(String worldName) throws SQLException;
}