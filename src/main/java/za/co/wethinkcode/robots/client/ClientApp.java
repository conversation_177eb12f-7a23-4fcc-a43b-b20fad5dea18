package za.co.wethinkcode.robots.client;

import za.co.wethinkcode.robots.commands.Command;
import za.co.wethinkcode.robots.commands.DisconnectCommand;
import za.co.wethinkcode.robots.server.Response;

import java.io.*;
import java.net.Socket;
import java.util.*;

public class ClientApp {
    private static final int MAX_ROBOTS = 2;
    private static final List<String> VALID_ROBOT_TYPES = List.of("sniper", "tank");

    public static void main(String[] args) throws IOException {
        Scanner scanner = new Scanner(System.in);
        Map<String, String> robots = new HashMap<>();

        String host = prompt(scanner, "Hello! Welcome to RobotWorld. Please enter the IP address of the server you'd like to connect to:");
        int portNumber = promptInt(scanner, "Enter the port number:");

        try (
                Socket socket = new Socket(host, portNumber);
                PrintWriter out = new PrintWriter(socket.getOutputStream(), true);
                BufferedReader in = new BufferedReader(new InputStreamReader(socket.getInputStream()))
        ) {
            System.out.println("Ready for launch!");

            while (robots.size() < MAX_ROBOTS) {
                String robotName = prompt(scanner, "Enter a name for your robot:");
                if (!isValidRobotName(robotName)) {
                    System.out.println("Robot name must only contain letters and numbers. Please try again.");
                    continue;
                }

                String robotType = prompt(scanner, "Enter a type for your robot (sniper/tank):").toLowerCase().trim();
                if (!VALID_ROBOT_TYPES.contains(robotType)) {
                    System.out.println("Invalid robot type. Valid types are: sniper, tank. Please try again.");
                    continue;
                }

                RobotCommand launchCommand = new RobotCommand(robotName, "launch", List.of(robotType, "5", "5"));
                try {
                    out.println(launchCommand.toJson());
                } catch (IllegalArgumentException e) {
                    System.out.println("Invalid Command. Try again");
                    continue;
                }

                Response response = readServerResponse(in);
                System.out.println(response.toString());

                System.out.println("Launching your robot into the world 🚀");
                sleep(4000);
                System.out.println(response.object.get("result"));
                robots.put(robotName, robotType);
                System.out.println("To check what you can do: use 'help'\n");

                handleCommands(scanner, robotName, in, out);
                break;
            }

            if (robots.size() >= MAX_ROBOTS) {
                System.out.println("ERROR: Cannot launch more than " + MAX_ROBOTS + " robots.");
            }

        } catch (IOException e) {
            System.out.println("Connection failed: " + e.getMessage());
        }
    }

    private static void handleCommands(Scanner scanner, String robotName, BufferedReader in, PrintWriter out) throws IOException {
        while (true) {
            String input = prompt(scanner, "Enter command: ");
            RobotCommand command = RobotCommand.parse(robotName, input);
            if (executeCommand(command, in, out)) {
                break;
            }
        }
    }

    private static boolean executeCommand(RobotCommand command, BufferedReader in, PrintWriter out) throws IOException {
        if (command.isDisconnect()) {
            out.println(new DisconnectCommand().toJSONString());
            System.out.println("Server: " + readServerResponse(in).toString());
            return true;
        }

        if (command.isRestricted()) {
            System.out.println("This command can only be run by the server admin");
            return false;
        }

        try {
            out.println(command.toJson());
        } catch (IllegalArgumentException e) {
            System.out.println("Invalid Command. Try again");
            return false;
        }

        Response response = readServerResponse(in);
        System.out.println("Server: " + response.getMessage());

        if (command.requiresExtraResponse()) {
            Response extraResponse = readServerResponse(in);
            System.out.println("Server: " + extraResponse.getMessage());
        }

        return false;
    }

    private static Response readServerResponse(BufferedReader in) throws IOException {
        String jsonString = in.readLine();
        return Response.responseFromJSONString(jsonString);
    }

    private static String buildLaunchRequest(String robotName, String robotType) {
        return String.format("{\"robot\": \"%s\", \"command\": \"launch\", \"arguments\": [\"%s\", \"5\", \"5\"]}",
                robotName, robotType);
    }

    private static class RobotCommand {
        private final String robotName;
        private final String command;
        private final List<String> arguments;
        private static final Set<String> RESTRICTED_COMMANDS = Set.of("quit", "robots", "dump");

        private RobotCommand(String robotName, String command, List<String> arguments) {
            this.robotName = Objects.requireNonNull(robotName, "Robot name cannot be null");
            this.command = Objects.requireNonNull(command, "Command cannot be null");
            this.arguments = Objects.requireNonNullElse(arguments, List.of());
        }

        public static RobotCommand parse(String robotName, String input) {
            String[] parts = input.trim().split("\\s+");
            String command = parts[0].toLowerCase();
            List<String> arguments = parts.length > 1 ? Arrays.asList(Arrays.copyOfRange(parts, 1, parts.length)) : List.of();
            return new RobotCommand(robotName, command, arguments);
        }

        public String toJson() {
            StringBuilder argumentsJson = new StringBuilder("[");
            for (int i = 0; i < arguments.size(); i++) {
                argumentsJson.append("\"").append(arguments.get(i)).append("\"");
                if (i < arguments.size() - 1) {
                    argumentsJson.append(", ");
                }
            }
            argumentsJson.append("]");
            return String.format("{\"robot\": \"%s\", \"command\": \"%s\", \"arguments\": %s}",
                    robotName, command, argumentsJson);
        }

        public boolean isDisconnect() {
            return command.equalsIgnoreCase("disconnect");
        }

        public boolean isRestricted() {
            return RESTRICTED_COMMANDS.contains(command.toLowerCase());
        }

        public boolean requiresExtraResponse() {
            return command.contains("reload") || command.contains("repair");
        }
    }

    private static boolean isValidRobotName(String name) {
        return !name.trim().isEmpty() && name.matches("[a-zA-Z0-9]+");
    }

    private static String prompt(Scanner scanner, String message) {
        System.out.println(message);
        return scanner.nextLine();
    }

    private static int promptInt(Scanner scanner, String message) {
        System.out.println(message);
        while (!scanner.hasNextInt()) {
            System.out.println("Please enter a valid number:");
            scanner.next();
        }
        int value = scanner.nextInt();
        scanner.nextLine();
        return value;
    }

    private static void sleep(int millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException ignored) {}
    }
}