package za.co.wethinkcode.robots.dao;

/**
 * TODO: Implement RobotDAO Interface
 * 
 * Responsibilities:
 * - Define robot persistence operations
 * - Work with Robot domain objects
 * - Handle robot CRUD operations
 * 
 * Methods to implement:
 * - saveRobot(String worldName, Robot robot)
 * - findRobotByName(String worldName, String robotName)
 * - findAllRobots(String worldName)
 * - updateRobot(String worldName, Robot robot)
 * - deleteRobot(String worldName, String robotName)
 * - deleteAllRobots(String worldName)
 * - robotExists(String worldName, String robotName)
 */
public interface RobotDAO {
    // TODO: Define interface methods
}
