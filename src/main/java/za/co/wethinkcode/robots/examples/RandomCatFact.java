package za.co.wethinkcode.robots.examples;

import kong.unirest.HttpResponse;
import kong.unirest.JsonNode;
import kong.unirest.Unirest;
import kong.unirest.UnirestException;

/**
 * Example HTTP client using Unirest library (Section 2 requirement).
 * Demonstrates external API integration that will be useful for the Javalin-based
 * Robot World API implementation.
 *
 * Dependencies required in pom.xml:
 * - com.konghq:unirest-java
 *
 * This example shows how to:
 * - Make HTTP GET requests using Unirest
 * - Handle HTTP status codes and response bodies
 * - Parse JSON responses
 * - Implement proper error handling
 */
public class RandomCatFact {

    private static final String CAT_FACT_API_URL = "https://catfact.ninja/fact";

    public static void main(String[] args) {
        System.out.println("Fetching random cat fact from " + CAT_FACT_API_URL);
        System.out.println("=" .repeat(50));

        try {
            fetchCatFact();
        } catch (Exception e) {
            System.err.println("Failed to fetch cat fact: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Fetches a random cat fact from the catfact.ninja API using Unirest.
     * Prints both the HTTP status code and the response body as required.
     */
    private static void fetchCatFact() {
        try {
            // Send GET request to catfact.ninja/fact
            HttpResponse<JsonNode> response = Unirest.get(CAT_FACT_API_URL)
                    .header("Accept", "application/json")
                    .header("User-Agent", "RobotWorld-JavaClient/1.0")
                    .asJson();

            // Print HTTP status code (requirement)
            System.out.println("HTTP Status Code: " + response.getStatus());
            System.out.println("HTTP Status Text: " + response.getStatusText());

            // Print response body (requirement)
            if (response.isSuccess()) {
                JsonNode body = response.getBody();
                System.out.println("Response Body: " + body.toString());

                // Extract and display the cat fact nicely
                if (body.getObject().has("fact")) {
                    String fact = body.getObject().getString("fact");
                    System.out.println("\nCat Fact: " + fact);
                }

                if (body.getObject().has("length")) {
                    int length = body.getObject().getInt("length");
                    System.out.println("Fact Length: " + length + " characters");
                }
            } else {
                System.err.println("Request failed with status: " + response.getStatus());
                System.err.println("Response body: " + response.getBody());
            }

        } catch (UnirestException e) {
            System.err.println("HTTP request failed: " + e.getMessage());
            throw new RuntimeException("Failed to fetch cat fact", e);
        } catch (Exception e) {
            System.err.println("Unexpected error: " + e.getMessage());
            throw new RuntimeException("Unexpected error while fetching cat fact", e);
        }
    }
}
