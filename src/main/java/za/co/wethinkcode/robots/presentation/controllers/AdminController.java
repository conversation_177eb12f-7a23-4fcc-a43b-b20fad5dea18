package za.co.wethinkcode.robots.presentation.controllers;

import za.co.wethinkcode.robots.service.GameService;
import za.co.wethinkcode.robots.service.WorldService;

import java.sql.SQLException;
import java.util.Scanner;

/**
 * Handles admin console commands and user interactions.
 * Responsible for command parsing, user prompts, and console output formatting.
 * Part of the presentation layer - delegates business logic to service layer.
 */
public class AdminController {
    private final GameService gameService;
    private final WorldService worldService;
    
    public AdminController(GameService gameService, WorldService worldService) {
        this.gameService = gameService;
        this.worldService = worldService;
    }
    
    /**
     * Main entry point for handling admin console commands.
     */
    public void handleAdminCommand(String commandLine, Scanner scanner) {
        String[] parts = commandLine.trim().split("\\s+");
        if (parts.length == 0) {
            System.out.println("No command entered.");
            return;
        }
        
        String command = parts[0].toLowerCase();
        
        // Route to specific handlers
        switch (command) {
            case "quit", "exit" -> handleQuitCommand();
            case "robots" -> handleRobotsCommand();
            case "dump" -> handleDumpCommand();
            case "display" -> handleDisplayCommand();
            case "purge" -> handlePurgeCommand(parts);
            case "save" -> handleSaveCommand(parts, scanner);
            case "restore" -> handleRestoreCommand(parts);
            case "help" -> handleHelpCommand();
            default -> System.out.println("Unknown admin command: " + command);
        }
    }
    
    /**
     * Handles server shutdown command.
     */
    private void handleQuitCommand() {
        System.out.println("Shutting down server...");
        gameService.shutdown();
    }
    
    /**
     * Displays information about all robots in the world.
     */
    private void handleRobotsCommand() {
        String robotsInfo = gameService.getAllRobotsInfo();
        System.out.println("Current Robots:");
        System.out.println(robotsInfo.isEmpty() ? "No robots in world" : robotsInfo);
    }
    
    /**
     * Displays full world state information.
     */
    private void handleDumpCommand() {
        String worldState = gameService.getFullWorldState();
        System.out.println("World State:");
        System.out.println(worldState);
    }
    
    /**
     * Displays visual representation of the world.
     */
    private void handleDisplayCommand() {
        gameService.displayWorld();
    }
    
    /**
     * Handles robot removal command.
     */
    private void handlePurgeCommand(String[] parts) {
        if (parts.length < 2) {
            System.out.println("Usage: purge <robotName>");
            return;
        }
        
        String robotName = parts[1];
        try {
            boolean removed = gameService.removeRobot(robotName);
            if (removed) {
                System.out.println("Robot '" + robotName + "' removed successfully.");
            } else {
                System.out.println("Robot '" + robotName + "' not found.");
            }
        } catch (Exception e) {
            System.out.println("Failed to remove robot: " + e.getMessage());
        }
    }
    
    /**
     * Handles world save command with user interaction.
     */
    private void handleSaveCommand(String[] parts, Scanner scanner) {
        try {
            // Parse world name from command or prompt user
            String worldName = extractWorldName(parts, scanner);
            if (worldName == null) {
                System.out.println("Save cancelled.");
                return;
            }
            
            // Check if world exists and handle overwrite confirmation
            boolean forceOverwrite = handleOverwriteConfirmation(worldName, scanner);
            
            // Delegate to service layer
            worldService.saveCurrentWorld(worldName, forceOverwrite);
            System.out.println("World '" + worldName + "' saved successfully.");
            
        } catch (Exception e) {
            System.out.println("Failed to save world: " + e.getMessage());
        }
    }
    
    /**
     * Handles world restore command.
     */
    private void handleRestoreCommand(String[] parts) {
        if (parts.length < 2) {
            System.out.println("Usage: restore <worldName>");
            return;
        }
        
        String worldName = parts[1];
        try {
            worldService.restoreWorld(worldName);
            System.out.println("World '" + worldName + "' restored successfully.");
        } catch (Exception e) {
            System.out.println("Failed to restore world: " + e.getMessage());
        }
    }
    
    /**
     * Displays help information for admin commands.
     */
    private void handleHelpCommand() {
        System.out.println("Available admin commands:");
        System.out.println("  robots    - List all robots in the world");
        System.out.println("  dump      - Show full world state");
        System.out.println("  display   - Show world visualization");
        System.out.println("  save <name> - Save current world");
        System.out.println("  restore <name> - Restore saved world");
        System.out.println("  purge <robot> - Remove robot from world");
        System.out.println("  quit      - Shutdown server");
        System.out.println("  help      - Show this help");
    }
    
    /**
     * Extracts world name from command arguments or prompts user.
     */
    private String extractWorldName(String[] parts, Scanner scanner) {
        if (parts.length >= 2 && !parts[1].trim().isEmpty()) {
            return parts[1].trim();
        }
        
        // Prompt user for world name
        System.out.print("Enter world name: ");
        String input = scanner.nextLine().trim();
        return input.isEmpty() ? null : input;
    }
    
    /**
     * Handles overwrite confirmation for existing worlds.
     */
    private boolean handleOverwriteConfirmation(String worldName, Scanner scanner) {
        try {
            if (!worldService.worldExists(worldName)) {
                return false; // No need to overwrite
            }
            
            System.out.print("World '" + worldName + "' already exists. Overwrite? (y/N): ");
            String response = scanner.nextLine().trim().toLowerCase();
            return response.equals("y") || response.equals("yes");
            
        } catch (Exception e) {
            System.out.println("Error checking world existence: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Prints the admin command prompt.
     */
    public static void printAdminPrompt() {
        System.out.println("Valid Commands: 'quit', 'robots', 'dump', 'display', 'purge <robot name>', 'save <worldName>', 'restore <worldName>'");
        System.out.print("[Admin]: ");
    }
}
