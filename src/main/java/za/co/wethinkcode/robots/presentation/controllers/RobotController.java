package za.co.wethinkcode.robots.presentation.controllers;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import za.co.wethinkcode.robots.commands.Command;
import za.co.wethinkcode.robots.service.GameService;
import za.co.wethinkcode.robots.server.Response;

/**
 * Handles robot command requests from clients.
 * Responsible for request parsing, validation, and response formatting.
 * Part of the presentation layer - delegates business logic to service layer.
 */
public class RobotController {
    private final GameService gameService;
    private final ObjectMapper objectMapper;
    
    public RobotController(GameService gameService) {
        this.gameService = gameService;
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * Main entry point for handling robot commands from network requests.
     */
    public JsonNode handleRobotCommand(String request) {
        try {
            // Parse and validate JSON request
            JsonNode jsonRequest = objectMapper.readTree(request);
            
            if (!jsonRequest.has("command")) {
                return createErrorResponse("Missing command field");
            }
            
            String command = jsonRequest.get("command").asText().toLowerCase();
            
            // Route to specific handlers based on command type
            return switch (command) {
                case "launch" -> handleLaunchCommand(jsonRequest);
                case "forward", "back" -> handleMoveCommand(jsonRequest);
                case "turn" -> handleTurnCommand(jsonRequest);
                case "state" -> handleStateCommand(jsonRequest);
                case "look" -> handleLookCommand(jsonRequest);
                case "fire" -> handleFireCommand(jsonRequest);
                case "repair" -> handleRepairCommand(jsonRequest);
                case "reload" -> handleReloadCommand(jsonRequest);
                case "disconnect" -> handleDisconnectCommand(jsonRequest);
                default -> handleGenericCommand(jsonRequest);
            };
            
        } catch (Exception e) {
            return createErrorResponse("Invalid request: " + e.getMessage());
        }
    }
    
    /**
     * Handles robot launch commands with specific validation.
     */
    private JsonNode handleLaunchCommand(JsonNode jsonRequest) {
        if (!jsonRequest.has("robot") || !jsonRequest.has("arguments")) {
            return createErrorResponse("Launch command requires robot name and type");
        }
        
        Command command = convertToCommand(jsonRequest);
        Response response = gameService.executeCommand(command);
        return convertToJsonNode(response);
    }
    
    /**
     * Handles movement commands with step validation.
     */
    private JsonNode handleMoveCommand(JsonNode jsonRequest) {
        if (!jsonRequest.has("robot") || !jsonRequest.has("arguments")) {
            return createErrorResponse("Move command requires robot name and steps");
        }
        
        // Validate step count
        JsonNode args = jsonRequest.get("arguments");
        if (args.size() == 0) {
            return createErrorResponse("Move command requires number of steps");
        }
        
        try {
            int steps = args.get(0).asInt();
            if (steps <= 0) {
                return createErrorResponse("Steps must be positive");
            }
        } catch (Exception e) {
            return createErrorResponse("Invalid step count");
        }
        
        Command command = convertToCommand(jsonRequest);
        Response response = gameService.executeCommand(command);
        return convertToJsonNode(response);
    }
    
    /**
     * Handles turn commands.
     */
    private JsonNode handleTurnCommand(JsonNode jsonRequest) {
        if (!jsonRequest.has("robot")) {
            return createErrorResponse("Turn command requires robot name");
        }
        
        Command command = convertToCommand(jsonRequest);
        Response response = gameService.executeCommand(command);
        return convertToJsonNode(response);
    }
    
    /**
     * Handles state query commands.
     */
    private JsonNode handleStateCommand(JsonNode jsonRequest) {
        if (!jsonRequest.has("robot")) {
            return createErrorResponse("State command requires robot name");
        }
        
        Command command = convertToCommand(jsonRequest);
        Response response = gameService.executeCommand(command);
        return convertToJsonNode(response);
    }
    
    /**
     * Handles look commands.
     */
    private JsonNode handleLookCommand(JsonNode jsonRequest) {
        if (!jsonRequest.has("robot")) {
            return createErrorResponse("Look command requires robot name");
        }
        
        Command command = convertToCommand(jsonRequest);
        Response response = gameService.executeCommand(command);
        return convertToJsonNode(response);
    }
    
    /**
     * Handles fire commands.
     */
    private JsonNode handleFireCommand(JsonNode jsonRequest) {
        if (!jsonRequest.has("robot")) {
            return createErrorResponse("Fire command requires robot name");
        }
        
        Command command = convertToCommand(jsonRequest);
        Response response = gameService.executeCommand(command);
        return convertToJsonNode(response);
    }
    
    /**
     * Handles repair commands.
     */
    private JsonNode handleRepairCommand(JsonNode jsonRequest) {
        if (!jsonRequest.has("robot")) {
            return createErrorResponse("Repair command requires robot name");
        }
        
        Command command = convertToCommand(jsonRequest);
        Response response = gameService.executeCommand(command);
        return convertToJsonNode(response);
    }
    
    /**
     * Handles reload commands.
     */
    private JsonNode handleReloadCommand(JsonNode jsonRequest) {
        if (!jsonRequest.has("robot")) {
            return createErrorResponse("Reload command requires robot name");
        }
        
        Command command = convertToCommand(jsonRequest);
        Response response = gameService.executeCommand(command);
        return convertToJsonNode(response);
    }
    
    /**
     * Handles disconnect commands.
     */
    private JsonNode handleDisconnectCommand(JsonNode jsonRequest) {
        Command command = convertToCommand(jsonRequest);
        Response response = gameService.executeCommand(command);
        return convertToJsonNode(response);
    }
    
    /**
     * Fallback handler for commands that don't need special validation.
     */
    private JsonNode handleGenericCommand(JsonNode jsonRequest) {
        Command command = convertToCommand(jsonRequest);
        Response response = gameService.executeCommand(command);
        return convertToJsonNode(response);
    }
    
    /**
     * Converts Jackson JsonNode to Command object via org.json.JSONObject.
     */
    private Command convertToCommand(JsonNode jsonRequest) {
        try {
            String jsonString = objectMapper.writeValueAsString(jsonRequest);
            org.json.JSONObject jsonObj = new org.json.JSONObject(jsonString);
            return Command.fromJSON(jsonObj);
        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to convert request to command", e);
        }
    }
    
    /**
     * Converts Response object to Jackson JsonNode.
     */
    private JsonNode convertToJsonNode(Response response) {
        try {
            return objectMapper.readTree(response.toJSONString());
        } catch (Exception e) {
            return createErrorResponse("Failed to process response: " + e.getMessage());
        }
    }
    
    /**
     * Creates standardized error responses.
     */
    private JsonNode createErrorResponse(String message) {
        try {
            String errorJson = "{\"result\":\"ERROR\",\"data\":{\"message\":\"" + message + "\"}}";
            return objectMapper.readTree(errorJson);
        } catch (Exception e) {
            throw new RuntimeException("Failed to create error response", e);
        }
    }
}
