package za.co.wethinkcode.robots.presentation.display;

/**
 * TODO: Implement WorldDisplayService
 * 
 * Responsibilities:
 * - Handle world visualization and display
 * - Format world state for console output
 * - Extract display logic from World.java
 * - Manage console output formatting
 * 
 * Extract from: World.java (displayWorld, displayViewport methods)
 */
public class WorldDisplayService {
    // TODO: Implementation needed
    // Extract displayWorld() and related methods from World.java
    public void displayWorld() {
        System.out.println(displayViewport(-halfWidth, halfHeight, width, height));
    }

    public String displayViewport(int originX, int originY, int viewWidth, int viewHeight){
        String[][] grid = buildBaseGrid(originX, originY, viewWidth, viewHeight);
        addObstacles(grid, originX,originY);
        addRobots(grid, originX, originY);
        return gridToString(grid);
    }
}
