package za.co.wethinkcode.robots.presentation.controllers;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.Socket;

/**
 * Handles TCP network connections and delegates command processing to RobotController.
 * Responsible for socket management, I/O streams, and network error handling.
 * Part of the presentation layer - no business logic or domain knowledge.
 */
public class NetworkController {
    private final RobotController robotController;
    private final ObjectMapper objectMapper;
    
    public NetworkController(RobotController robotController) {
        this.robotController = robotController;
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * Handles a client connection lifecycle including session management and cleanup.
     */
    public void handleClientConnection(Socket clientSocket) {
        String clientId = clientSocket.getRemoteSocketAddress().toString();
        System.out.println("New client connected: " + clientId);
        
        try (BufferedReader in = new BufferedReader(new InputStreamReader(clientSocket.getInputStream()));
             PrintWriter out = new PrintWriter(clientSocket.getOutputStream(), true)) {
            
            handleClientSession(in, out, clientId);
            
        } catch (IOException e) {
            System.out.println("Error handling client " + clientId + ": " + e.getMessage());
        } finally {
            closeClientConnection(clientSocket, clientId);
        }
    }
    
    /**
     * Manages the request-response cycle for a client session.
     */
    private void handleClientSession(BufferedReader in, PrintWriter out, String clientId) throws IOException {
        String request;
        while ((request = in.readLine()) != null) {
            try {
                // Log incoming request
                System.out.println("Client [" + clientId + "]: " + request);
                
                // Process request through controller
                JsonNode response = robotController.handleRobotCommand(request);
                
                // Send response
                String responseString = objectMapper.writeValueAsString(response);
                out.println(responseString);
                
                // Log outgoing response
                System.out.println("Response to [" + clientId + "]: " + responseString);
                
            } catch (Exception e) {
                // Handle request processing errors
                String errorResponse = createNetworkErrorResponse("Request processing failed: " + e.getMessage());
                out.println(errorResponse);
                System.out.println("Error processing request from " + clientId + ": " + e.getMessage());
            }
        }
    }
    
    /**
     * Safely closes client connection and logs disconnection.
     */
    private void closeClientConnection(Socket clientSocket, String clientId) {
        try {
            if (!clientSocket.isClosed()) {
                clientSocket.close();
            }
            System.out.println("Client disconnected: " + clientId);
        } catch (IOException e) {
            System.out.println("Error closing connection for " + clientId + ": " + e.getMessage());
        }
    }
    
    /**
     * Creates a standardized error response for network-level errors.
     */
    private String createNetworkErrorResponse(String message) {
        try {
            JsonNode errorNode = objectMapper.readTree(
                "{\"result\":\"ERROR\",\"data\":{\"message\":\"" + message + "\"}}"
            );
            return objectMapper.writeValueAsString(errorNode);
        } catch (Exception e) {
            // Fallback to simple string if JSON creation fails
            return "{\"result\":\"ERROR\",\"data\":{\"message\":\"Network error\"}}";
        }
    }
}
