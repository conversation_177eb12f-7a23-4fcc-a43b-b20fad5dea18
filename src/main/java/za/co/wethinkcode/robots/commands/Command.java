package za.co.wethinkcode.robots.commands;

import org.json.JSONArray;
import org.json.JSONObject;
import za.co.wethinkcode.robots.Robot;

import java.util.Arrays;

/**
 * Abstract representation of a command sent to robots.
 * Defines interface and common behavior for all commands.
 */
public abstract class Command {
    private final Robot robot;
    private final String[] arguments;

    public Command(Robot robot, String[] arguments) {
        this.robot = robot;
        this.arguments = arguments;
    }

    public Robot getRobot() {
        return robot;
    }

    public String[] getArguments() {
        return arguments;
    }

    public static boolean isValidCommand(String command) {
        return switch (command.toLowerCase()) {
            case "forward", "back", "turn", "look", "state", "launch", "dump", "orientation", "shutdown",
                 "disconnect", "fire", "repair", "reload", "help" -> true;
            default -> false;
        };
    }

    public String toJSONString() {
        JSONObject json = new JSONObject();
        json.put("command", commandName().toLowerCase());
        json.put("arguments", arguments);

        if (robot != null) {
            json.put("robot", robot.getName());
        }

        return json.toString();
    }

public static Command fromJSON(JSONObject json) {
    String command = extractCommand(json);
    if (isDisconnectCommand(command)) {
        return new DisconnectCommand();
    }

    String robotName = extractRobotName(json);
    String[] args = extractArguments(json);

    return createCommand(command, robotName, args);
}

private static String extractCommand(JSONObject json) {
    return json.getString("command").toLowerCase();
}

private static boolean isDisconnectCommand(String command) {
    return command.equals("disconnect");
}

private static String extractRobotName(JSONObject json) {
    return json.getString("robot");
}

private static String[] extractArguments(JSONObject json) {
    JSONArray jsonArgs = json.getJSONArray("arguments");
    String[] args = new String[jsonArgs.length()];
    for (int i = 0; i < jsonArgs.length(); i++) {
        args[i] = jsonArgs.getString(i);
    }
    return args;
}

    private static final String[] EMPTY_ARGS = new String[0];

    private static Command createCommand(String command, String robotName, String[] args) {
        Robot robot = new Robot(robotName);

        return switch (command) {
            case "repair" -> new RepairCommand(robot, args);
            case "reload" -> new ReloadCommand(robot, args);
            case "help" -> new HelpCommand(robot, EMPTY_ARGS);
            case "dump" -> new DumpCommand(robot, EMPTY_ARGS);
            case "look" -> new LookCommand(robot, EMPTY_ARGS);
            case "state" -> new StateCommand(robot, EMPTY_ARGS);
            case "launch" -> createLaunchCommand(robotName, args);
            case "forward", "back" -> new MoveCommand(robot, command, args);
            case "turn" -> new TurnCommand(robot, args);
            case "orientation" -> new OrientationCommand(robot);
            case "off" -> new ShutdownCommand(robot, EMPTY_ARGS);
            case "fire" -> new FireCommand(robot, args);
            default -> throw new IllegalArgumentException("Unknown command: " + command);
        };
    }

    private static Command createLaunchCommand(String robotName, String[] args) {
        return new LaunchCommand(new Robot(robotName, args[0]), args);
    }

public static Command fromInput(String[] arguments) {
    String[] tokens = getTokens(arguments);
    String command = tokens[0];
    String robotName = arguments[1];

    validateCommand(tokens);

    if (command.equals("disconnect")) return  new DisconnectCommand();


    String robot = robotName;

    String[] args = Arrays.copyOfRange(tokens, 1, tokens.length);

    return buildCommand(robot, command, args);
}

private static String[] getTokens(String[] arguments) {
    return arguments[0].trim().split(" ");
}

private static void validateCommand(String[] tokens) {
    if (tokens[0].isEmpty()) {
        throw new IllegalArgumentException("Invalid or empty command ");
    }
}

private static String[] dispatchCommand(String command, String[] tokens, String robotName) {
    String lowerCommand = command.toLowerCase();
    if (isForwardOrBackCommand(lowerCommand)) {
        return handleForwardBack(tokens, robotName);
    }
    if (isTurnCommand(lowerCommand)) {
        return handleTurn(tokens, robotName);
    }
    if (isSimpleCommand(lowerCommand)) {
        return handleSimpleCommand(tokens, robotName);
    }
    if (isLaunchCommand(lowerCommand)) {
        return handleLaunch(tokens, robotName);
    }
    return new String[]{};
}

private static boolean isForwardOrBackCommand(String command) {
    return "forward".equals(command) || "back".equals(command);
}

private static boolean isTurnCommand(String command) {
    return "turn".equals(command);
}

private static boolean isSimpleCommand(String command) {
    return switch (command) {
        case "state", "look", "orientation", "fire", "repair", "reload", "off" -> true;
        default -> false;
    };
}

private static boolean isLaunchCommand(String command) {
    return "launch".equals(command);
}

private static Command buildCommand(String robot, String command, String[] args) {
    return fromJSON(new JSONObject()
            .put("robot", robot)
            .put("command", command)
            .put("arguments", new JSONArray(Arrays.asList(args))));
}

private static String[] handleForwardBack(String[] tokens, String robotName) {
    if (tokens.length >= 3) {
        // direction, robot, steps
        return new String[]{tokens[1], tokens[0], tokens[1], tokens[2]};
    } else if (robotName != null) {
        return new String[]{robotName, tokens[0], robotName, tokens[1]};
    }
    return new String[]{"", tokens[0]};
}

private static String[] handleTurn(String[] tokens, String robotName) {
    String robot = tokens.length >= 3 ? tokens[1] : robotName;
    String arg = tokens.length >= 3 ? tokens[2] : tokens[1];
    return new String[]{robot, arg};
}

private static String[] handleSimpleCommand(String[] tokens, String robotName) {
    String robot = tokens.length > 1 ? tokens[1] : robotName;
    return new String[]{robot};
}

public static String[] handleLaunch(String[] tokens, String robotName) {
    String robot = tokens.length >= 3 ? tokens[2] : robotName;
    String robotType = tokens.length >= 2 ? tokens[1] : "";
    return new String[]{robot, robotType};
}

    public abstract String commandName();
}
