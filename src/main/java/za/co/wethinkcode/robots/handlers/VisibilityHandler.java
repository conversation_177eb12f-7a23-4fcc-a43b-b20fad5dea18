package za.co.wethinkcode.robots.handlers;

import org.json.JSONArray;
import org.json.JSONObject;
import za.co.wethinkcode.robots.Robot;
import za.co.wethinkcode.robots.client.Direction;
import za.co.wethinkcode.robots.server.Obstacle;
import za.co.wethinkcode.robots.server.ObstacleType;
import za.co.wethinkcode.robots.server.Response;
import za.co.wethinkcode.robots.server.World;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Handles visibility logic for robots in the world.
 * Determines which objects (robots, obstacles, or world edges) are visible from a given robot's position
 * in each cardinal direction within a defined viewing range. Filters and formats data for look command responses.
 */

public class VisibilityHandler {
    private final World world;

    public VisibilityHandler(World world) {
        this.world = world;
    }

    public Response lookAround(Robot robot) {
        List<Map<String, Object>> visibleObjects = new ArrayList<>();
        int maxDistance = world.getVisibility();

        StringBuilder messageBuilder = buildInitialMessage(robot);

        for (Direction.CardinalDirection direction : Direction.CardinalDirection.values()) {
            List<Map<String, Object>> visibleInDirection = checkVisibleObjects(robot, direction, maxDistance);
            visibleObjects.addAll(processVisibleObjects(visibleInDirection, messageBuilder));
        }

        completeMessage(visibleObjects, messageBuilder, robot, maxDistance);
        return buildResponse(robot, maxDistance, visibleObjects, messageBuilder.toString());
    }

    private List<Map<String, Object>> checkVisibleObjects(Robot robot, Direction.CardinalDirection direction, int maxDistance) {
        return scanDirection(robot, direction, maxDistance).stream()
                .sorted(Comparator.comparingInt(o -> (int) o.get("distance")))
                .collect(Collectors.toList());
    }

    private List<Map<String, Object>> scanDirection(Robot robot, Direction.CardinalDirection direction, int maxDistance) {
        List<Map<String, Object>> visibleObjects = new ArrayList<>();
        int[] directionVector = calculateDirectionVector(direction);
        int startX = robot.getX();
        int startY = robot.getY();

        for (int step = 1; step <= maxDistance; step++) {
            int x = startX + directionVector[0] * step;
            int y = startY + directionVector[1] * step;

            Optional<Map<String, Object>> obstacle = checkForObstacle(x, y, step, direction);
            Optional<Map<String, Object>> otherRobot = checkForRobot(robot, x, y, step, direction);
            Optional<Map<String, Object>> edge = checkForEdge(x, y, step, direction);

            obstacle.ifPresent(visibleObjects::add);
            otherRobot.ifPresent(visibleObjects::add);
            edge.ifPresent(visibleObjects::add);

            if (obstacle.isPresent() && isMountain(obstacle.get())) {
                break;
            }
        }
        return visibleObjects;
    }

    private boolean isMountain(Map<String, Object> object) {
        return object.get("object") instanceof Obstacle obstacle &&
                obstacle.type() == ObstacleType.MOUNTAIN;
    }

    private Optional<Map<String, Object>> checkForObstacle(int x, int y, int step, Direction.CardinalDirection direction) {
        return world.getObstacles().stream()
                .filter(obs -> !isObjectAlreadyRecorded())
                .filter(obs -> isPositionInObstacle(obs, x, y))
                .findFirst()
                .map(obs -> createObjectMap("OBSTACLE", direction, step, obs));
    }

    private Optional<Map<String, Object>> checkForRobot(Robot currentRobot, int x, int y, int step, Direction.CardinalDirection direction) {
        return world.getRobots().stream()
                .filter(r -> !r.equals(currentRobot))
                .filter(r -> r.getX() == x && r.getY() == y)
                .findFirst()
                .map(r -> createObjectMap("ROBOT", direction, step, r));
    }

    private Optional<Map<String, Object>> checkForEdge(int x, int y, int step, Direction.CardinalDirection direction) {
        if (isAtEdge(x, y, direction)) {
            return Optional.of(createObjectMap("EDGE", direction, step, direction));
        }
        return Optional.empty();
    }

    private boolean isObjectAlreadyRecorded() {
        return false; // Simplified - would check against recorded objects in real implementation
    }

    private boolean isPositionInObstacle(Obstacle obs, int x, int y) {
        for (int obX = obs.getX(); obX < obs.getMaxX(); obX++) {
            for (int obY = obs.getY(); obY < obs.getMaxY(); obY++) {
                if (obY == y && obX == x) {
                    return true;
                }
            }
        }
        return false;
    }

    private Map<String, Object> createObjectMap(String type, Direction.CardinalDirection direction,
                                                int distance, Object object) {
        Map<String, Object> map = new HashMap<>();
        map.put("type", type);
        map.put("direction", direction);
        map.put("distance", distance);
        map.put("object", object);
        return map;
    }

    private int[] calculateDirectionVector(Direction.CardinalDirection direction) {
        return switch (direction) {
            case EAST -> new int[]{1, 0};
            case WEST -> new int[]{-1, 0};
            case NORTH -> new int[]{0, 1};
            case SOUTH -> new int[]{0, -1};
        };
    }

    private boolean isAtEdge(int x, int y, Direction.CardinalDirection dir) {
        return switch (dir) {
            case NORTH -> y >= world.getHalfHeight();
            case SOUTH -> y <= -world.getHalfHeight();
            case EAST -> x >= world.getHalfWidth();
            case WEST -> x <= -world.getHalfWidth();
        };
    }

    // Helper methods for lookAround
    private StringBuilder buildInitialMessage(Robot robot) {
        StringBuilder builder = new StringBuilder();
        builder.append("\nLooking around for ").append(robot.getName()).append(" 🤖:");
        builder.append("\n  Objects").append(":");
        return builder;
    }

    private List<Map<String, Object>> processVisibleObjects(List<Map<String, Object>> visibleInDirection, StringBuilder messageBuilder) {
        visibleInDirection.forEach(obj -> {
            String type = (String) obj.get("type");
            Direction.CardinalDirection dir = (Direction.CardinalDirection) obj.get("direction");
            int distance = (int) obj.get("distance");

            String emoji = type.equalsIgnoreCase("obstacle") ? "🚧" :
                    type.equalsIgnoreCase("robot") ? "🤖" : "🧭";

            messageBuilder.append("\n   ").append(emoji).append(" Found ")
                    .append(type.equalsIgnoreCase("obstacle") ? "an obstacle" :
                            type.equalsIgnoreCase("robot") ? "another robot" : "the edge of the world")
                    .append(" nearby!")
                    .append("\n       🧭 Direction ").append(dir.symbolForDirection())
                    .append("\n       🦶 Steps ").append(distance);
        });
        return visibleInDirection;
    }

    private void completeMessage(List<Map<String, Object>> visibleObjects, StringBuilder messageBuilder, Robot robot, int maxDistance) {
        if (visibleObjects.isEmpty()) {
            messageBuilder.append("\n   🥲 Could not find anything, try moving around to find more objects");
        }
        String snapshot = world.displayDirectionalCross(robot, maxDistance);
        messageBuilder.append("\nHere is a snapshot of you can see:").append("\n").append(snapshot);
    }

    private Response buildResponse(Robot robot, int maxDistance, List<Map<String, Object>> visibleObjects, String message) {
        Response response = new Response("OK", message);
        JSONObject data = new JSONObject();
        JSONArray position = new JSONArray();

        position.put(robot.getPosition().getX());
        position.put(robot.getPosition().getY());

        data.put("visibility", maxDistance);
        data.put("position", position);
        data.put("objects", visibleObjects);

        response.object.put("data", data);
        JSONObject state = new JSONObject();
        state.put("position", position);

        response.object.put("state", state);
        return response;
    }
}