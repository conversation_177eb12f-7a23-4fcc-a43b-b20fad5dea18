package za.co.wethinkcode.robots.handlers;

import org.json.JSONArray;
import org.json.JSONObject;
import za.co.wethinkcode.robots.Robot;
import za.co.wethinkcode.robots.client.Position;
import za.co.wethinkcode.robots.server.*;
import za.co.wethinkcode.robots.commands.*;

import java.util.*;

public class CommandHandler {
    @FunctionalInterface
    public interface CompletionHandler {
        void onComplete(Response response);
    }

    @FunctionalInterface
    private interface CommandProcessor {
        void process(Command command, CompletionHandler handler);
    }

    private final World world;
    private final VisibilityHandler visibilityHandler;
    private final Map<String, CommandProcessor> commandProcessors;

    public CommandHandler(World world) {
        this.world = world;
        this.visibilityHandler = new VisibilityHandler(this.world);
        this.commandProcessors = initializeCommandProcessors();
    }

    private Map<String, CommandProcessor> initializeCommandProcessors() {
        Map<String, CommandProcessor> processors = new HashMap<>();

        processors.put("help", (command, handler) -> handleHelp(handler));
        processors.put("launch", (command, handler) -> handleLaunch((LaunchCommand) command, handler));
        processors.put("state", (command, handler) -> handleState(command.getRobot().getName(), handler));
        processors.put("orientation", (command, handler) -> handleOrientation(command.getRobot().getName(), handler));
        processors.put("look", (command, handler) -> handleLook(command.getRobot().getName(), handler));
        processors.put("forward", (command, handler) -> handleMove((MoveCommand) command, handler));
        processors.put("back", (command, handler) -> handleMove((MoveCommand) command, handler));
        processors.put("turn", (command, handler) -> handleTurn((TurnCommand) command, command.getRobot().getName(), handler));
        processors.put("off", (command, handler) -> handleShutdown((ShutdownCommand) command, handler));
        processors.put("disconnect", (command, handler) -> handler.onComplete(new Response("OK", "Client disconnected.")));
        processors.put("fire", (command, handler) -> handleFire(command.getRobot(), handler));
        processors.put("reload", (command, handler) -> handleReload((ReloadCommand) command, handler));
        processors.put("repair", (command, handler) -> handleRepair((RepairCommand) command, handler));

        return processors;
    }
/**
 * Handles commands by directing each command to its specific handling logic.
 * This method uses a command registry pattern to determine the
 * specific type of Command and calls the corresponding handler method.*/

public void handle(Command command, CompletionHandler handler) {
    System.out.println("Executing command: " + command.commandName());

    processCommand(command, handler);
}

private void processCommand(Command command, CompletionHandler handler) {
    String commandName = command.commandName();
    CommandProcessor processor = commandProcessors.get(commandName);

    if (processor != null) {
        processor.process(command, handler);
    } else {
        handler.onComplete(new Response("ERROR", "Unsupported command"));
    }
}

    private void handleHelp(CompletionHandler handler) {
        String helpText = String.join("\n",
                """
                         🌸🤖✨ I CAN UNDERSTAND THESE COMMANDS 🌸🤖✨
                        ┌────────────────────┬──────────────────────────────────────────────┐
                         COMMAND             | DESCRIPTION
                        ├────────────────────┼──────────────────────────────────────────────┤ 
                        |1.❓ help           | Show this help message 🆘                    
                        ├────────────────────┼──────────────────────────────────────────────┤
                        |2.🧭 orientation    | What direction you are facing   
                        ├────────────────────┼──────────────────────────────────────────────┤ 
                        |3.forward <name> <n>| Move forward by n steps (max 5) ⏩   
                        ├────────────────────┼──────────────────────────────────────────────┤ 
                        |4.back <name> <n>   | Move backward by n steps (max 5) ⏪                    
                        ├────────────────────┼──────────────────────────────────────────────┤
                        |5. left             | Turn left 🔄  e.g. turn <name> left 
                        ├────────────────────┼──────────────────────────────────────────────┤ 
                        |6. right            | Turn right 🔁 e.g. turn <name> right                 
                        ├────────────────────┼──────────────────────────────────────────────┤
                        |7. look             | List visible objects 👀   
                        ├────────────────────┼──────────────────────────────────────────────┤ 
                        |8. state            | Show current robot status 📊                    
                        ├────────────────────┼──────────────────────────────────────────────┤
                        |9. fire             | Fire a shot (tank or sniper rules) 🔫   
                        ├────────────────────┼──────────────────────────────────────────────┤
                        |7. reload           | Refill your ammo to maximum 🔄💥   
                        ├────────────────────┼──────────────────────────────────────────────┤ 
                        |8. repair           | Restore your shields (takes time) 🛠️🛡️                    
                        ├────────────────────┼──────────────────────────────────────────────┤
                        |9. disconnect       | Disconnect the client completely 🫤   
                        ├────────────────────┼──────────────────────────────────────────────┤
                        |10. launch          | Launch another robot 🚀 e.g. <type> <name>
                        ├────────────────────┼──────────────────────────────────────────────┤
                                
                        """
        );

        handler.onComplete(new Response("OK", helpText));
    }

private void handleLaunch(LaunchCommand command, CompletionHandler completionHandler) {
    Status status = addRobotToWorld(command);
    Response response = createLaunchResponse(status, command);

    if (status == Status.OK) {
        updateRobotState(command, response);
        ensureEmptyDataObject(response);
    }

    completionHandler.onComplete(response);
}

private Status addRobotToWorld(LaunchCommand command) {
    return world.addRobot(command.getRobot());
}

private Response createLaunchResponse(Status status, LaunchCommand command) {
    return switch (status) {
        case WorldFull -> new Response("ERROR", "No more space in this world");
        case HitObstaclePIT -> new Response("ERROR", "Fell");
        case OK -> new Response("OK", "Launched " + command.getRobot().getName() + " into the world");
        case ExistingName -> new Response("ERROR", "Too many of you in this world");
        case OutOfBounds -> new Response("ERROR", "Failed to launch " + command.getRobot().getName() + " because it crashed outside of the world");
        case HitObstacle -> new Response("ERROR", "Failed to launch " + command.getRobot().getName() + " Obstructed");
        case HitRobot -> new Response("ERROR", "Failed to launch " + command.getRobot().getName() + " because it a robot");
    };
}

private void updateRobotState(LaunchCommand command, Response response) {
    world.stateForRobot(command.getRobot(), response);
}

private void ensureEmptyDataObject(Response response) {
    // For launch command, ensure data object is empty
    JSONObject emptyData = new JSONObject();
    response.object.put("data", emptyData);
}

private void handleMove(MoveCommand command, CompletionHandler handler) {
    if (!isValidCommandFormat(command, handler)) {
        return;
    }

    String direction = command.commandName();
    String robotName = command.getRobot().getName();
    int steps = parseSteps(command.getArguments()[0]);

    if (!isValidDirection(direction, handler)) {
        return;
    }

    if (!isValidStepCount(steps, handler)) {
        return;
    }

    Robot robot = world.findRobot(robotName);
    if (!isRobotFound(robot, handler)) {
        return;
    }
    if (!isRobotAlive(robot, handler)) {
        return;
    }
    if (!isRobotReady(robot, handler)) {
        return;
    }

    Position previousPosition = new Position(robot.getX(), robot.getY());
    if (!moveRobot(command, robot, handler)) {
        return; // moveRobot already sent the response (hit boundary, obstacle, etc.)
    }

    Response response = new Response("OK", "Done");
    world.stateForRobot(robot, response);

    handler.onComplete(response);
}

private boolean isValidCommandFormat(MoveCommand command, CompletionHandler handler) {
    if (command.getArguments().length != 1) {
        handler.onComplete(new Response("ERROR", "Invalid move command format. Only include the number of steps as an int in the arguments"));
        return false;
    }
    return true;
}

private boolean isValidDirection(String direction, CompletionHandler handler) {
    if (!direction.equals("forward") && !direction.equals("back")) {
        handler.onComplete(new Response("ERROR", "Invalid move direction. Use 'forward' or 'back'."));
        return false;
    }
    return true;
}

private boolean isValidStepCount(int steps, CompletionHandler handler) {
    if (steps < 1 || steps > 5) {
        handler.onComplete(new Response("ERROR", "You can only move a maximum of 5 steps."));
        return false;
    }
    return true;
}

private boolean isRobotFound(Robot robot, CompletionHandler handler) {
    if (robot == null) {
        handler.onComplete(new Response("ERROR", "Robot not found."));
        return false;
    }
    return true;
}

private boolean isRobotAlive(Robot robot, CompletionHandler handler) {
    if (robot.status == Robot.RobotStatus.Dead) {
        handler.onComplete(new Response("ERROR", robot.getName() + " is DEAD and cannot move."));
        return false;
    }
    return true;
}

private boolean isRobotReady(Robot robot, CompletionHandler handler) {
    if (robot.status == Robot.RobotStatus.Reload) {
        handler.onComplete(new Response("ERROR", robot.getName() + " is reloading and cannot move"));
        return false;
    }
    if (robot.status == Robot.RobotStatus.Repair) {
        handler.onComplete(new Response("ERROR", robot.getName() + " is repairing and cannot move"));
        return false;
    }
    return true;
}

private boolean moveRobot(MoveCommand command, Robot robot, CompletionHandler handler) {
    if (command.commandName().equals("forward") || command.commandName().equals("back")) {
        return moveInDirection(command, robot, handler);
    }else {
        handler.onComplete(new Response("ERROR", "Invalid move command."));
        return false;
    }
}

private boolean moveInDirection(MoveCommand command, Robot robot, CompletionHandler handler) {
    int steps = Integer.parseInt(command.getArguments()[0]);
    boolean isForward = command.commandName().equals("forward");

    if (!areFuturePositionsValid(robot, steps, isForward, handler)) {
        return false;
    }

    moveRobot(robot, steps, isForward);
    return true;
}

private boolean areFuturePositionsValid(Robot robot, int steps, boolean isForward, CompletionHandler handler) {
    // First, check all positions the robot will move through
    Position currentPosition = robot.getPosition();
    Position lastValidPosition = currentPosition;
    for (int i = 1; i <= steps; i++) {
        Position nextPosition = calculateNextPosition(currentPosition, robot, isForward, i);
        Status status = world.isPositionValid(nextPosition, robot);

        if (status != Status.OK) {
            if (status == Status.HitObstacle) {
                robot.setPosition(lastValidPosition.getX(), lastValidPosition.getY());
            } else if (status == Status.HitObstaclePIT) {
                robot.setPosition(nextPosition.getX(), nextPosition.getY());
                robot.status = Robot.RobotStatus.Dead;
            }
            // Handle the specific error case
            handlePositionError(robot, lastValidPosition, status, handler);
            return false;
        }
        lastValidPosition = nextPosition;
    }
    return true;
}


    private void moveRobot(Robot robot, int steps, boolean isForward) {
    // All positions are valid, now actually move the robot
    for (int i = 0; i < steps; i++) {
        if (isForward) {
            robot.moveForward(1);
        } else {
            robot.moveBackward(1);
        }
    }
}

private Position calculateNextPosition(Position currentPosition, Robot robot, boolean isForward, int stepCount) {
    int dx = calculateDx(robot.orientation(), isForward, stepCount);
    int dy = calculateDy(robot.orientation(), isForward, stepCount);

    return new Position(currentPosition.getX() + dx, currentPosition.getY() + dy);
}

private int calculateDx(String orientation, boolean isForward, int stepCount) {
    int dx = 0;
    switch (orientation) {
        case "EAST" -> dx = isForward ? stepCount : -stepCount;
        case "WEST" -> dx = isForward ? -stepCount : stepCount;
    }
    return dx;
}

private int calculateDy(String orientation, boolean isForward, int stepCount) {
    int dy = 0;
    switch (orientation) {
        case "NORTH" -> dy = isForward ? stepCount : -stepCount;
        case "SOUTH" -> dy = isForward ? -stepCount : stepCount;
    }
    return dy;
}

private void handlePositionError(Robot robot, Position currentPosition, Status status, CompletionHandler handler) {
    Response response;

    switch (status) {
        case HitObstaclePIT -> {
            robot.status = Robot.RobotStatus.Dead;
            response = new Response("OK", "Fell");
        }
        case HitObstacle -> {
            response = new Response("OK", "Obstructed");
        }
        case OutOfBounds -> {
            response = new Response("OK", "At the " + robot.orientation().toUpperCase() + " edge");
        }
        default -> {
            response = new Response("ERROR", "Invalid position");
        }
    }

    world.stateForRobot(robot, response);
    handler.onComplete(response);
}


    private void handleState(String robotName, CompletionHandler completionHandler) {
        Robot robot = world.findRobot(robotName);
        if (robot != null) {
            String message = "\n" +
                    "State for " + robotName + " 🤖:" +
                    "\n" +
                    " 🌎 Position: [" + robot.getX() + "," + robot.getY() + "]" +
                    "\n" +
                    " 🧭 Direction: " + robot.getDirection().getDirection().symbolForDirection() +
                    "\n" +
                    " 🛡️ Shields: " + robot.getShields() +
                    "\n" +
                    " 🔫 Shots: " + robot.getShots() +
                    "\n" +
                    " 📋 Status: " + robot.status.toString().toUpperCase() +
                    "\n";

            Response response = new Response("OK", message);
            world.stateForRobot(robot, response);

            completionHandler.onComplete(response);
        } else {
             completionHandler.onComplete(new Response("ERROR", "Could not find robot: " + robotName));
        }
    }

    private void handleLook(String robotName, CompletionHandler completionHandler) {
        if (robotName == null || robotName.isBlank()) {
            // Get the first robot in the world (if any) and use its name
            List<Robot> robots = world.getRobots();
            if (robots.isEmpty()) {
                 completionHandler.onComplete(new Response("ERROR", "No robots available in the world."));
                 return;
            }

            robotName = robots.getFirst().getName();
        }

        Robot robot = world.findRobot(robotName);

        if (robot == null) {
            completionHandler.onComplete(new Response("ERROR", "Could not find robot: " + robotName));
            return;
        }

        completionHandler.onComplete(visibilityHandler.lookAround(robot));
    }

    private void handleOrientation(String robotName, CompletionHandler completionHandler) {
        Robot robot = world.findRobot(robotName);
        if (robot != null) {
            String direction = robot.orientation(); // Get the current direction
            completionHandler.onComplete(new Response("OK", robot.getName() + " is facing " + direction + "."));
        } else {
            completionHandler.onComplete(new Response("ERROR", "Could not find robot: " + robotName));
        }
    }

    private void handleTurn(TurnCommand turnCommand, String robotName, CompletionHandler completionHandler) {
        Response response;

        if (turnCommand.getArguments().length > 0) {
            String directionInput = turnCommand.getArguments()[0].toLowerCase();

            Robot robot = world.findRobot(robotName);
            if (robot != null) {
                if (robot.status == Robot.RobotStatus.Reload) {
                    response = new Response("ERROR", robot.getName() + " is reloading and cannot turn");
                }

                if (robot.status == Robot.RobotStatus.Repair) {
                    response = new Response("ERROR", robot.getName() + " is repairing and cannot turn");
                }

                response = switch (directionInput) {
                    case "left" -> {
                        robot.turnLeft();
                        yield new Response("OK", robot.getName() + " turned left to " + robot.orientation());
                    }
                    case "right" -> {
                        robot.turnRight();
                        yield new Response("OK", robot.getName() + " turned right to " + robot.orientation());
                    }
                    default -> new Response("ERROR", "Invalid direction. Use 'left' or 'right'.");
                };

                world.stateForRobot(robot, response);
            } else {
                response = new Response("ERROR", "Robot not found: " + robotName);
            }
        } else {
            response = new Response("ERROR", "Missing direction for turn command.");
        }

        completionHandler.onComplete(response);
    }

    private void handleFire(Robot robot, CompletionHandler completionHandler) {
        robot = this.world.findRobot(robot.getName());

        if (robot == null) {
            completionHandler.onComplete(new Response("ERROR", "Could not find robot: " + robot.getName()));
            return;
        }

        if (robot.getShots() <= 0) {
            completionHandler.onComplete(new Response("ERROR", "You have no shots remaining."));
            return;
        }

        if (robot.status == Robot.RobotStatus.Reload) {
            completionHandler.onComplete(new Response("ERROR", robot.getName() + " is reloading and cannot fire"));
            return;
        }

        if (robot.status == Robot.RobotStatus.Repair) {
            completionHandler.onComplete(new Response("ERROR", robot.getName() + " is repairing and cannot fire"));
            return;
        }

        Position robotP = robot.getPosition();
        String direction = robot.orientation();

        int dx = 0;
        int dy = 0;

        switch (direction) {
            case "NORTH" -> dy = 1;
            case "SOUTH" -> dy = -1;
            case "EAST" -> dx = 1;
            case "WEST" -> dx = -1;
        }
        int range;

        if ("tank".equalsIgnoreCase(robot.getMake())) {
            range = 6 - robot.getShots();
        } else {
            range = robot.getShots() - 9;
        }

        // Reduce the shot count
        robot.setShots(robot.getShots() - 1);
        Robot hitRobot = null;
        int distance = 0;

        // Check for hits
        for (int step = 1; step <= range; step++) {
            Position checkPos = new Position(robotP.getX() + step * dx, robotP.getY() + step * dy);

            for (Robot otherRobot : world.getRobots()) {
                if (!otherRobot.getName().equals(robot.getName()) && otherRobot.getPosition().equals(checkPos)) {
                    hitRobot = otherRobot;
                    break;
                }
            }

            if (hitRobot != null) {
                distance = step;
                break; // Stop checking after a hit
            }
        }

        // Handle the result of the shot
        if (hitRobot == null) {
            Response response = new Response("OK", "Miss");

            world.stateForRobot(robot, response);

            JSONObject data = new JSONObject();
            data.put("message", "Miss");

            response.object.put("data", data);

            completionHandler.onComplete(response);
            return;
        }

        // Apply damage
        hitRobot.takeHit(); // This reduces shield strength or kills the robot

        Response response;
        Response hitRobotResponse = new Response("OK", "I got hit");

        if (hitRobot.isDead()) {
           response = new Response("OK", "You have hit 💥 " + hitRobot.getName() + "! It is now destroyed.");
        } else {
            response = new Response("OK", "You have hit 💥 " + hitRobot.getName() + "! Remaining shield: " + hitRobot.getShields());
        }

        JSONObject data = new JSONObject();

        world.stateForRobot(hitRobot, hitRobotResponse);
        world.stateForRobot(robot, response);

        data.put("message", "Hit");
        data.put("distance", distance);
        data.put("robot", hitRobot.getName());
        data.put("state", hitRobotResponse.object.getJSONObject("state"));

        response.object.put("data", data);

        completionHandler.onComplete(response);
    }

    private static int getRange(Robot robot) {
        if ("tank".equalsIgnoreCase(robot.getMake())) {
            return 6 - robot.getShots();
        } else {
            return robot.getShots() - 9;
        }
    }

    public void handleShutdown(ShutdownCommand command, CompletionHandler completionHandler) {
        String robotName = command.getRobot().getName();
        completionHandler.onComplete(world.removeRobot(robotName));
    }

    private void handleRepair(RepairCommand command, CompletionHandler completionHandler) {
        Robot robot = world.findRobot(command.getRobot().getName());
        if (robot == null) {
            completionHandler.onComplete(new Response("ERROR", "Robot not found: " + command.getRobot().getName()));
            return;
        }

        // Check if the robot is already repairing
        if (robot.isRepairing()) {
            completionHandler.onComplete(new Response("ERROR", robot.getName() + " is already repairing."));
            return;
        }

        robot.setRepairing(true);
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                robot.setShields(world.getMaxShieldStrength()); // Repair to max shields
                robot.setRepairing(false);
                Response response = new Response("OK", robot.getName() + " has finished repairing");
                world.stateForRobot(robot, response);

                completionHandler.onComplete(response);
            }
        }, world.getShieldRepairTime() * 1000L); // Repair time in milliseconds

        completionHandler.onComplete(new Response("OK", robot.getName() + " is now repairing."));
    }

    private void handleReload(ReloadCommand command, CompletionHandler completionHandler) {
        Robot robot = world.findRobot(command.getRobot().getName());

        if (robot == null) {
            completionHandler.onComplete(new Response("ERROR", "Robot not found: " + command.getRobot().getName()));
            return;
        }

        if (robot.isReloading()) {
            completionHandler.onComplete(new Response("ERROR", robot.getName() + " is already reloading."));
            return;
        }

        robot.setReloading(true);

        completionHandler.onComplete(new Response("OK", robot.getName() + " is now reloading."));

        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                robot.setReloading(false);
                robot.setShots(robot.getMaxShots());
                Response response = new Response("OK", robot.getName() + " is done.");

                world.stateForRobot(robot, response);
                completionHandler.onComplete(response);
            }
        }, world.getReloadTime() * 1000L); // Repair time in milliseconds
    }

    private int parseSteps(String arg) {
        try {
            return Integer.parseInt(arg);
        } catch (NumberFormatException e) {
            return -1;
        }
    }
}